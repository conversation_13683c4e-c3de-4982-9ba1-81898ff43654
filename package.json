{"name": "ad-decisioning", "version": "2.128.11", "description": "doc v 0.13", "main": "index.js", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> build", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node build/main", "check-types": "tsc --noEmit", "prettier": "prettier --check \"{src,test}/**/*.ts\"", "prettier:fix": "prettier --write \"{src,test}/**/*.ts\"", "lint": "eslint \"{src,test}/**/*.ts\" --max-warnings=0", "lint:fix": "eslint \"{src,test}/**/*.ts\" --fix", "lint-test": "run-s check-types prettier lint test", "test:code-quality": "run-s check-types prettier lint", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "coverage": "jest --testResultsProcessor ../node_modules/jest-junit --coverage --coverageDirectory=../output/coverage", "prepare": "husky"}, "repository": {"type": "git", "url": "ssh://********************:7999/adt/ad-decisioning.git"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.823.0", "@fastify/static": "^8.1.1", "@iabtcf/core": "^1.5.6", "@joi/date": "^2.1.1", "@nestjs/common": "^11.1.1", "@nestjs/core": "^11.1.1", "@nestjs/platform-fastify": "^11.1.1", "@nestjs/schedule": "^6.0.0", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^11.2.0", "adpod-aws": "^1.43.0", "adpod-tools": "^1.202.0", "axios": "^1.9.0", "cache-manager": "^7.0.0", "cors": "^2.8.5", "dayjs": "^1.11.13", "dd-trace": "^5.52.0", "dotenv": "^16.5.0", "envalid": "^8.0.0", "helmet": "^8.1.0", "joi": "^17.13.3", "js-sha256": "0.11.0", "lodash": "^4.17.21", "nestjs-joi": "^1.11.0", "newrelic": "^12.18.2", "node-color-log": "^10.0.2", "rxjs": "^7.8.2"}, "devDependencies": {"@automock/adapters.nestjs": "^2.1.0", "@automock/jest": "^2.1.0", "@golevelup/ts-jest": "^0.7.0", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.1", "@swc/core": "^1.11.24", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/node": "^22.15.19", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "compression": "^1.8.0", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-junit": "^16.0.0", "lint-staged": "^16.0.0", "mockdate": "^3.0.5", "nodemon": "^3.1.10", "npm-run-all": "^4.1.5", "prettier": "^3.5.3", "proxy-agent": "^6.5.0", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "tslib": "^2.8.1", "typescript": "^5.8.3", "unplugin-swc": "^1.5.3", "uuid": "^11.1.0"}, "volta": {"node": "22.15.0"}, "engines": {"node": ">=22.15.0"}, "gitHooks": {"pre-commit": "npx lint-staged"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"]}, "jest": {"moduleFileExtensions": ["js", "mjs", "cjs", "jsx", "ts", "tsx", "json", "node"], "rootDir": "src", "testRegex": ".*\\.test\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coveragePathIgnorePatterns": ["src/interceptors", "src/middleware", "src/interfaces", "src/models", "src/main.ts", "src/app.module.ts", "src/assets/configs/appLogs.config.ts", "src/initSwagger.ts", "src/components/info/info.controller.ts", ".doc.decorator.ts"], "coverageDirectory": "../coverage", "testEnvironment": "node", "coverageReporters": ["text", "cobertura", "lcov"], "testPathIgnorePatterns": ["<rootDir>/src/assets/scenarios", "<rootDir>/src/assets/logs"]}, "jest-junit": {"suiteName": "jest tests", "outputDirectory": "output/coverage", "outputName": "junit.xml", "classNameTemplate": "{classname} - {title}", "titleTemplate": "{classname} - {title}", "ancestorSeparator": " > ", "usePathForSuiteName": "true"}}