name: tests

on:
  pull_request:
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.ref_name }}
  cancel-in-progress: true

env:
  NODE_VERSION: '22.15.0'
  NPM_CACHE: 'npm'

jobs:
  unit-test:
    runs-on:
      group: k8s-runner-group-cwp
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: ${{ env.NPM_CACHE }}

      - name: Install dependencies
        run: npm ci

      - name: Unit test
        run: npm run test

  code-quality-test:
    runs-on:
      group: k8s-runner-group-cwp
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: ${{ env.NPM_CACHE }}

      - name: Install dependencies
        run: npm ci

      - name: Test Code Quality
        run: npm run test:code-quality

  sonarcloud:
    continue-on-error: true
    runs-on:
      group: k8s-runner-group-cwp
    needs:
      - unit-test
      - code-quality-test
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: SonarCloud Scan
        uses: SonarSource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

      - name: SonarQube Quality Gate check
        uses: sonarsource/sonarqube-quality-gate-action@master
        with:
          pollingTimeoutSec: 600
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
