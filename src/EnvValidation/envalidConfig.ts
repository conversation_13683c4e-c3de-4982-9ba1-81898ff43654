import * as dotenv from 'dotenv';

dotenv.config();
import { cleanEnv, port, url, str, host, num, bool } from 'envalid';
import { megaBytes, between0And100, minutes } from 'adpod-tools';
import { base64ToAscii, coreReducer, seconds } from 'adpod-tools/dist/utils/envalidHelpers';

export const validators = cleanEnv(process.env, {
  // CACHE
  REFRESH_CACHE_INTERVAL: minutes({ default: 32 }),
  REFRESH_CACHE_INTERVAL_OFFSET: seconds({ default: 60 }),
  CHECK_CONFIGS_INTERVAL: minutes({ default: 4 }),
  CACHE_SIZE_DAYS: num({ default: 2 }),
  CACHE_BUCKET_FETCH_DAYS_LIMIT: num({ default: 7 }),
  CACHE_MAX_SIZE: num({ default: 4000 }),
  DEBUG_CACHE_TTL: seconds({ default: 10 }),
  CACHE_CONFIGS_TO_SET_LOCALLY: str({
    default: ''
  }),

  // AWS
  AWS_ACCESS_KEY_ID: str({ default: '' }),
  AWS_SECRET_ACCESS_KEY: str({ default: '' }),

  // AWS / S3
  S3_BUCKET_NAME: str({ default: '' }),
  S3_BUCKET_REGION: str({ default: '' }),
  START_BID_S3_FETCH: str({
    default: 'DISABLED',
    choices: ['ENABLED', 'DISABLED']
  }),
  S3_WORKER_CONFIG_PATH: str({ default: 'apm-configs/workerConfig.json' }),

  // FW
  FW_URL: url({ default: 'https://5e529.v.fwmrm.net/ad/g/1' }),
  FILLERS_ADS_HOST: str({ default: '$HTTP_PROTOCOL://dai-discoveryengage.tvn.pl' }),

  // AD PROMO SERVER
  AD_PROMO_SERVER_URL: url({ default: 'https://stage-wake.tvn.pl/rel/startover' }),

  // AWS / ROLE
  ROLE_ARN: str({ default: '' }),
  ROLE_SESSION_NAME: str({ default: '' }),
  ROLE_EXTERNAL_ID: str({ default: '' }),
  ROLE_DURATION_SECONDS: num({ default: 900 }),

  // REDIS
  REDIS_HOST: host({
    default: ''
  }),
  REDIS_PORT: port({ default: 6379 }),
  REDIS_PASS: str({ default: '' }),
  REDIS_CLUSTER_UPDATE_STATUS_KEY: str({ default: 'redisClusterUpdateStatus' }),
  REDIS_CLUSTER_UPDATE_STATUS_KEY_TTL: seconds({ default: 10 }),
  REDIS_CLUSTER_UPDATE_STATUS_RETRY_INTERVAL: num({ default: 10000 }),
  REDIS_CACHE_BREAK_RESPONSE_KEY: str({ default: 'cacheBreak' }),
  REDIS_MAX_CONNECTION_RETRYS: num({ default: 500 }),

  // PROFILER
  PROFILER_ENABLED: bool({ default: false }),
  PROFILER_REFRESH_CONFIG_INTERVAL: minutes({ default: 60 }),
  PROFILER_CONFIG_PATH: str({ default: 'profiler/profiler_config.json' }),
  PROFILER_AWS_IP_URL: url({
    default: 'http://***************/latest/meta-data/local-ipv4'
  }),

  // WORKER CONFIG
  WORKER_CONFIG_INTERVAL_UPDATE_SECONDS: seconds({ default: 300 }), // update every 5 min
  WORKER_CONFIG_TTL: minutes({ default: 10 }),

  BASE_ADO_URL: str({ default: '$HTTP_PROTOCOL://tvn.adocean.pl/ad.xml' }),

  // APP
  MASTER_PORT: port({ default: 10060 }),
  SLAVE_PORT: port({ default: 3420 }),
  APP_HOST: host({ default: '0.0.0.0' }),
  APP_NAME: str({ default: 'adpod-worker' }),
  BODY_LIMIT: megaBytes({ default: 50 }),
  REQUEST_TIMEOUT_MS: num({ default: 10000 }),
  AD_OCEAN_REQUEST_TIMEOUT_MS: num({ default: 60000 }),
  REQUEST_TIMEOUT: str({
    default: 'DISABLED',
    choices: ['ENABLED', 'DISABLED']
  }),

  // OTHER
  EXCLUDE_CONFIGS_LOAD_WITH_FILENAME_CONTAINING: str({ default: '' }),
  LOG_ADSERVER_RESPONSE_PROCESSING_TIME_WARN_THRESHOLD: num({ default: 2000 }),

  // procent of workers that could be enough for health checking
  HEALTH_CHECK_LIMIT: between0And100({ default: 50 }),

  PREFETCH_OFFSET: num({ default: 60 }),
  CUST_PARAMS_VALIDATION_EXCLUDES: str({ default: '' }),
  PREFETCH_NEXT_REPLACED_OFFSET: num({ default: 2 }),

  INACTIVE_CORS: coreReducer({ default: 0 }),
  APP_CACHE_DEAP_PROFILES: str({
    default: 'DISABLED',
    choices: ['ENABLED', 'DISABLED']
  }),

  NODE_ENV: str({ default: 'local' }),
  DD_SERVICE_NAME: str({ default: 'Worker' }),

  HOSTNAME: str({ default: '' }),

  GAM_CERT: base64ToAscii({ default: '' }),
  GAM_KEY: base64ToAscii({ default: '' }),

  PL_MEDIA_ENHANCED_URL: str({
    default:
      'http://pl.media.enhanced.live/vast/vast_$UNIVERSAL_AD_ID/vast_$UNIVERSAL_AD_ID.xml'
  }),
  IT_MEDIA_ENHANCED_URL: str({
    default:
      'http://it.media.enhanced.live/vast/vast_$UNIVERSAL_AD_ID/vast_$UNIVERSAL_AD_ID.xml'
  }),
  FW_AD_PROGRAMMATIC_INDICATOR: str({
    default: 'progmod'
  })
});
