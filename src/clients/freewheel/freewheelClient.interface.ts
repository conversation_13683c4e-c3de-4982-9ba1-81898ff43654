import { Channel } from 'adpod-tools';
import {
  Overwrite,
  RequestHeaders,
  VMAP,
  Vmap,
  VmapAdBreak,
  VmapAdBreakNormalized
} from '../../interfaces';
import { DeapItemType } from '../../scripts/services/deapProfiles.service';

export abstract class IFreewheelClient {
  abstract getDurationBasedAds(
    version: string,
    channel: Channel,
    headers: RequestHeaders,
    duration: number,
    deapProfiles: DeapItemType,
    uid?: string
  ): Promise<{ response: FWDurationBasedVmapNormalized | null; url: string }>;
}

export type FWDurationBasedVmap = Overwrite<Vmap, { 'vmap:VMAP': FWDurationBasedVMAP }>;

export type FWDurationBasedVMAP = Overwrite<VMAP, { 'vmap:AdBreak': VmapAdBreak }>;

export type FWDurationBasedVmapNormalized = Overwrite<
  Vmap,
  {
    'vmap:VMAP': FWDurationBasedVMAPNormalized;
  }
>;

export type FWDurationBasedVMAPNormalized = Overwrite<
  VMAP,
  { 'vmap:AdBreak': VmapAdBreakNormalized }
>;
