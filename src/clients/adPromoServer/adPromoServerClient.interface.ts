import { Channel } from 'adpod-tools';
import {
  Overwrite,
  RequestHeaders,
  VMAP,
  Vmap,
  VmapAdBreak,
  VmapAdBreakNormalized
} from '../../interfaces';

export abstract class IAdPromoServerClient {
  abstract getFillerAds(
    channel: Channel,
    duration: number,
    headers: RequestHeaders
  ): Promise<{ response: AdPromoServerDurationBasedVmapNormalized | null; url: string }>;
}

export type AdPromoServerDurationBasedVmap = Overwrite<
  Vmap,
  {
    'vmap:VMAP': AdPromoServerDurationBasedVMAP;
  }
>;

export type AdPromoServerDurationBasedVMAP = Overwrite<VMAP, { 'vmap:AdBreak': VmapAdBreak }>;

export type AdPromoServerDurationBasedVmapNormalized = Overwrite<
  Vmap,
  {
    'vmap:VMAP': AdPromoServerDurationBasedVMAPNormalized;
  }
>;

export type AdPromoServerDurationBasedVMAPNormalized = Overwrite<
  VMAP,
  {
    'vmap:AdBreak': VmapAdBreakNormalized;
  }
>;
