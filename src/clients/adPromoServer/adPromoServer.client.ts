import { Channel, LogLevel, normalizeVast4, request, xmlParser } from 'adpod-tools';
import logger from '../../libs/logging/logger';
import { URLParamsHelper } from '../../scripts/adserver/urlHelper';
import { RequestHeaders } from '../../interfaces';
import {
  AdPromoServerDurationBasedVmap,
  AdPromoServerDurationBasedVmapNormalized,
  IAdPromoServerClient
} from './adPromoServerClient.interface';
import { validators } from '../../EnvValidation/envalidConfig';

export class AdPromoServerClient implements IAdPromoServerClient {
  async getFillerAds(
    channel: Channel,
    duration: number,
    headers: RequestHeaders
  ): Promise<{ response: AdPromoServerDurationBasedVmapNormalized | null; url: string }> {
    const url = new URLParamsHelper(validators.AD_PROMO_SERVER_URL, '&')
      .add('dur', duration)
      .add('ch', channel)
      .toString();

    try {
      logger('SO_ADPROMO_REQUEST', { url, headers }, LogLevel.startOver);

      const response = await request(url, {
        headers: {
          'user-agent': headers['x-device-user-agent'],
          'x-device-user-agent': headers['x-device-user-agent']
        }
      });

      const responseText = (await response.text()) as string;

      logger(
        'SO_ADPROMO_RESPONSE',
        {
          responseStatus: response.status,
          responseStatusText: response.statusText,
          responseHeaders: response.statusText,
          responseOk: response.ok,
          responseType: response.type,
          responseUrl: response.url,
          responseText
        },
        LogLevel.startOver
      );

      const jsonResult = xmlParser.fromXMLtoJSON(
        responseText
      ) as AdPromoServerDurationBasedVmap;

      logger('SO_ADPROMO_XML_TO_JSON', { xmlToJson: jsonResult }, LogLevel.startOver);

      return { response: this.normalizeAdPromoServerDurationBasedVmap(jsonResult), url };
    } catch (err) {
      logger('ERROR_SO_ADPROMO_FETCH', { err }, LogLevel.error);
      return { response: null, url };
    }
  }

  private normalizeAdPromoServerDurationBasedVmap(
    obj: AdPromoServerDurationBasedVmap
  ): AdPromoServerDurationBasedVmapNormalized {
    const adData = obj['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource']['vmap:VASTAdData'];

    obj['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource']['vmap:VASTAdData'] =
      normalizeVast4(adData);

    return obj as AdPromoServerDurationBasedVmapNormalized;
  }
}
