import { NestFastifyApplication } from '@nestjs/platform-fastify';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import pckg from 'package.json';
import { PlaylistMultipleModule } from './components/playlist-multiple/playlistMultiple.module';
import { PlaylistSingleModule } from './components/playlist-single/playlistSingle.module';
import { WhatsonModule } from './components/whatson/whatson.module';

export function initSwagger(app: NestFastifyApplication): void {
  const config = new DocumentBuilder()
    .setTitle('AdPod Worker')
    .setVersion(pckg.description)
    .build();

  const document = SwaggerModule.createDocument(app, config, {
    include: [PlaylistMultipleModule, PlaylistSingleModule, WhatsonModule]
  });
  SwaggerModule.setup('api/doc', app, document);
}
