import { Vast4Normalized } from 'adpod-tools';
import { AdserverAd } from './configuration.interface';
import { VmapNormalized } from './vmap.type';

export type IVastJsonOperationalData = {
  vast4Json: Vast4Normalized;
  isWithReplacedAds: boolean;
  adServerResponseLog: AdserverAd[];
};

export enum TrackingScriptsEnum {
  tracking = 'tracking',
  impression = 'impression'
}

export type slotFWTrackingScriptsType = {
  adIndex: number;
  name: string;
  value: string;
  type: TrackingScriptsEnum | null;
};

export type TrackingType = {
  _attributes: { event: string };
  _cdata: string;
};

export type VastJsonPlaylistType = Vast4Normalized | VmapNormalized;
