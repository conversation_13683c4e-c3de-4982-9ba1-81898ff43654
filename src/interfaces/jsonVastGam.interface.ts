export type VastGam = {
  VAST: {
    _attributes: {
      'xmlns:xsi': string;
      'xsi:noNamespaceSchemaLocation': string;
      version: string;
    };
    Ad?: AdVastGam;
  };
};

export type AdVastGam = {
  _attributes: {
    id: string;
  };
  Wrapper: Wrapper;
};

type Wrapper = {
  AdSystem: TextType;
  Advertiser: TextType;
  VASTAdTagURI: CDATAType; // redirect url
  Error: CDATAType;
  Impression: CDATAType;
  Category: Category[];
  Creatives: Creatives;
  Extensions: {
    Extension: Extension[];
  };
};

export type Extension = {
  _attributes: {
    type: string;
  };
  Country?: TextType;
  Bandwidth?: TextType;
  BandwidthKbps?: TextType;
  AttributionText?: TextType;
  AttributionUrl?: CDATAType;
  PreviousAdInformation?: TextType;
  VisibleUrl?: CDATAType;
  UI?: UI;
  ConversionUrl?: CDATAType;
  QueryId?: TextType;
  WhyThisAd?: {
    WtaClickThroughUrl: CDATAType;
  };
  FeEventId?: TextType;
  AdEventId?: TextType;
  LineItemId?: TextType;
  DealId?: TextType;
  CustomTracking?: TrackingEvents;
  IconClickFallbackImages?: IconClickFallbackImages;
  UniversalAdId?: UniversalAdId;
};

type IconClickFallbackImages = {
  _attributes: {
    program: string;
  };
  IconClickFallbackImage: IconClickFallbackImage;
};
type UniversalAdId = {
  Id?: TextType;
  InCreativeAdId?: TextType;
};

type IconClickFallbackImage = {
  _attributes: {
    width: string;
    height: string;
  };
  AltText: TextType;
  StaticResource: StaticResource;
};

type UI = {
  config: {
    context: Context;
    params: Params;
  };
};

type Params = {
  attribution_text: Context;
  enable_companion_banner: {
    _attributes: {
      bool: string;
    };
  };
  attribution_url?: Context;
  signals: {
    _attributes: {
      int: string;
    };
  };
};

type Context = {
  _attributes: {
    data: string;
  };
};

type Creatives = {
  Creative: Creative;
};

export type Creative = {
  _attributes: {
    sequence: string;
  };
  Linear: {
    Duration: TextType;
    TrackingEvents: TrackingEvents;
    Icons: {
      Icon: Icon;
    };
  };
};

type Icon = {
  _attributes: {
    program: string;
    width: string;
    height: string;
    xPosition: string;
    yPosition: string;
  };
  StaticResource: StaticResource;
  IconClicks: {
    IconClickThrough: CDATAType;
  };
};

type StaticResource = {
  _attributes: {
    creativeType: string;
  };
} & CDATAType;

type TrackingEvents = {
  Tracking: Tracking[];
};

type Tracking = {
  _attributes: {
    event: string;
  };
} & CDATAType;

type Category = {
  _attributes: {
    authority: string;
  };
} & TextType;

type TextType = {
  _text: string;
};
type CDATAType = {
  _cdata: string;
};
