import { RequestMacroParams } from '../scripts/configuration/injectReqParams/requestMacroParams';

export interface IPlaylistInfoItem {
  isWithReplacedAds: boolean;
  bid: string;
  breakAllAdsCount: number;
  breakDaiPlaylistAdsCount: number;
  connector: string;
  breakAds: {
    p: number;
    id: string;
    universalAdId: string;
    campaignId: string | undefined;
    timeOffset: string | undefined;
    t: 'TV' | 'DAI';
    adServer: {
      url: string | undefined;
      fw_slotImpressionEvent: boolean;
      fw_slotEndEvent: boolean;
    };
  }[];
}

export interface IRequestLog {
  output: string;
  channel: string;
  configVersion: string;
  mode: string;
  requestMacroParams: RequestMacroParams;
  configId?: string;
  startFrom?: string;
}

export type IPlaylistResponse = {
  playlist: string;
  emptyVastReason: string | null;
  playlistInfo: (IPlaylistInfoItem | null)[] | IPlaylistInfoItem | null;
  requestLog: IRequestLog;
};
