import { Protocol } from '../models/protocol.model';
import { ParamType } from '../scripts/configuration/injectReqParams/requestMacroParams';

export type InjectedParams = {
  uid: ParamType;
  gdpr: ParamType;
  gdprConsent: ParamType;
  npa: ParamType;
  deviceUserAgent: ParamType;
  deviceIP: ParamType;
  deviceReferrer: ParamType;
  rand8: ParamType;
  httpProtocol?: Protocol;
  custParams: ParamType;
  ch: ParamType;
  appVersion: ParamType;
};
