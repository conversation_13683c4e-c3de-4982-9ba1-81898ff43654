import { Overwrite } from './helpers.type';
import { Vmap, VMAP, VmapAdBreak, VmapAdBreakNormalized } from './vmap.type';

type TextType = { _text: string };

type OptionalTextType = Partial<TextType>;

export type PrerollVmap = Overwrite<Vmap, { 'vmap:VMAP': PrerollVMAP }>;

export type PrerollVMAP = Overwrite<VMAP, { 'vmap:AdBreak': VmapAdBreak }> & PrerollVMAPDebug;

type PrerollVMAPDebug = {
  Debug?: {
    RAWPrerollRequestURL: OptionalTextType;
    PrerollRequestURL: TextType;
    ValidationResult: TextType;
    WhatsOnStatus: TextType;
  };
};

// PREROLL NORMALIZED

export type PrerollVmapNormalized = Overwrite<Vmap, { 'vmap:VMAP': PrerollVMAPNormalized }>;

export type PrerollVMAPNormalized = Overwrite<VMAP, { 'vmap:AdBreak': VmapAdBreakNormalized }>;
