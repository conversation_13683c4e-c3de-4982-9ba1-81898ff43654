import { DebugAdSlot, Vast4, Vast4Normalized } from 'adpod-tools';
import { Overwrite } from './helpers.type';
import { TrackingType } from './jsonVast.type';

export type Vmap = {
  'vmap:VMAP': VMAP;
};

export type VMAP = {
  _attributes: {
    'xmlns:vmap': string;
    version: string;
    prefetch?: string;
  };
  'vmap:AdBreak': VmapAdBreak | VmapAdBreak[];
};

export type VmapAdBreak = {
  _attributes: {
    timeOffset: string;
    breakType: string;
    breakId: string;
  };
  'vmap:TrackingEvents'?: {
    'vmap:Tracking': TrackingType[];
  };
  'vmap:AdSource': VmapAdSource;
  Debug?: DebugAdSlot;
};

export type VmapAdSource = {
  _attributes: {
    id: string;
    allowMultipleAds: string; // boolean
    followRedirects: string; // boolean
  };
  'vmap:VASTAdData': Vast4;
};

// VMAP NORMALIZED

export type VmapNormalized = Overwrite<Vmap, { 'vmap:VMAP': VMAPNormalized }>;

type VMAPNormalized = Overwrite<VMAP, { 'vmap:AdBreak': VmapAdBreakNormalized[] }>;

export type VmapAdBreakNormalized = Overwrite<
  VmapAdBreak,
  { 'vmap:AdSource': VmapAdSourceNormalized }
>;

type VmapAdSourceNormalized = Overwrite<VmapAdSource, { 'vmap:VASTAdData': Vast4Normalized }>;
