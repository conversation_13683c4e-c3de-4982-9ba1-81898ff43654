import { Protocol } from '../models/protocol.model';

// TODO: add to adpod-tools:adCreative, check what is certain
export type Companions = {
  CompanionAds?: {
    Companion?: {
      _attributes?: { id: string };
      StaticResource?: {
        _attributes?: { type: string };
        _cdata?: string;
      };
      AdParameters?: { EPGMetadata: any };
    };
  };
};

export type PrerollEventsParams = {
  channel: string;
  version: string;
  requestProtocol: Protocol;
  custParamsValue: string;
  uid?: string;
  slotImpressionUrl?: string;
  slotEndUrl?: string;
};
