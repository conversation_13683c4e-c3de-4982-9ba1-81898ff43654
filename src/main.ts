import { NestFactory } from '@nestjs/core';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { FastifyInstance } from 'fastify';
import { LogLevel } from 'adpod-tools';
import helmet from 'helmet';
import cors from 'cors';
import compression from 'compression';
import cluster from 'cluster';
import os from 'os';
import { JoiPipe } from 'nestjs-joi';
import pckg from '../package.json';
import { AppModule } from './app.module';
import { InfoModule } from './components/info/info.module';
import { initSwagger } from './initSwagger';
import logger from './libs/logging/logger';
import { validators } from './EnvValidation/envalidConfig';
import { HttpExceptionFilter } from './libs/errors/http-exception.filter';
import { comprehensiveLoggingMiddleware } from './libs/middlewares';

require('newrelic');

const getAvailableCoresAmount = () => {
  return validators.NODE_ENV === 'local' ? 1 : os.cpus().length - validators.INACTIVE_CORS;
};

async function bootstrap() {
  if (cluster.isMaster) {
    const numCPUs = getAvailableCoresAmount();

    logger('RUN_MASTER_PROCESS', { numCPUs }, LogLevel.dev);

    for (let i = 0; i < numCPUs; i++) {
      cluster.fork();
    }

    cluster.on('exit', () => {
      logger('ERROR_WORKER_DIED', {}, LogLevel.error);

      // Prevent from dying all workers
      cluster.fork();
    });

    const infoFastifyAdapter = new FastifyAdapter({
      bodyLimit: validators.BODY_LIMIT
    });

    const infoApp = await NestFactory.create<NestFastifyApplication>(
      InfoModule,
      infoFastifyAdapter
    );

    // Add comprehensive logging middleware first to capture all requests
    await infoApp.register(async function (fastify: FastifyInstance) {
      fastify.addHook('onRequest', comprehensiveLoggingMiddleware);
    });

    infoApp.use(compression());
    infoApp.use(helmet());
    infoApp.use(
      cors({
        origin: true,
        credentials: true
      })
    );

    await infoApp.listen(validators.MASTER_PORT, validators.APP_HOST);
  } else {
    const fastifyAdapter = new FastifyAdapter({
      bodyLimit: validators.BODY_LIMIT
    });

    const app = await NestFactory.create<NestFastifyApplication>(AppModule, fastifyAdapter);

    // Add comprehensive logging middleware first to capture all requests
    await app.register(async function (fastify: FastifyInstance) {
      fastify.addHook('onRequest', comprehensiveLoggingMiddleware);
    });

    app.use(compression());
    app.use(helmet());
    app.use(
      cors({
        origin: true,
        credentials: true
      })
    );
    app.useGlobalPipes(new JoiPipe());
    app.useGlobalFilters(new HttpExceptionFilter());

    initSwagger(app);

    const port = validators.SLAVE_PORT;

    await app.listen(port, validators.APP_HOST);

    logger(
      'APP_STARTED',
      {
        port,
        processPid: process.pid,
        version: pckg.version
      },
      LogLevel.dev
    );
  }
}
// eslint-disable-next-line @typescript-eslint/no-floating-promises
bootstrap();
