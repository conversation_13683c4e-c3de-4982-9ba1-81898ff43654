import { IConfiguration, IAd, LogLevel } from 'adpod-tools';
import { cloneDeep } from 'lodash';
import { RequestHeaders } from '../../../interfaces';
import logger from '../../../libs/logging/logger';
import { RequestMacroParams } from './requestMacroParams';

export const injectReqParams = (
  configuration: IConfiguration,
  requestMacroParams: RequestMacroParams,
  headers: RequestHeaders,
  ip?: string,
  ua?: string | string[],
  extraParams?: Record<string, unknown>
): IConfiguration => {
  logger(
    'INJECT_REQ_PARAMS',
    {
      bid: configuration.id,
      requestParams: requestMacroParams,
      extraParams
    },
    LogLevel.dev
  );
  const configCopy: IConfiguration = cloneDeep(configuration);

  configCopy.adslot.forEach((adData: IAd, index: number) => {
    configCopy.adslot[index].vastmirroredadsJson =
      adData.vastmirroredadsJson &&
      JSON.parse(
        requestMacroParams.applyParams(
          JSON.stringify(adData.vastmirroredadsJson),
          headers,
          extraParams
        )
      );

    // Add req params to ad request
    configCopy.adslot[index].adrequest = requestMacroParams.applyParams(
      configCopy.adslot[index].adrequest,
      headers,
      undefined,
      ip,
      ua
    );
  });

  return configCopy;
};
