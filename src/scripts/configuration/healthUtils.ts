import { validators } from '../../EnvValidation/envalidConfig';

type ClusterState = {
  cluster: number;
  state: string;
};

/**
 * Calculates the percentage of healthy workers based on the number of clusters
 * and the HEALTH_CHECK_LIMIT from the environment validators.
 *
 * @param {ClusterState[]} clusters - An array of present clusters.
 * @return {number} The calculated limit of healthy workers.
 */
export const healthLimit = (clusters: ClusterState[]): number => {
  return Math.floor((clusters.length * validators.HEALTH_CHECK_LIMIT) / 100);
};
