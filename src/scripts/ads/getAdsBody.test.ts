import { getAdsBody } from './getAdsBody';
import { oneAdVast } from '../../assets/mocks/oneAdVast';
import { dur15AdVast } from '../../assets/mocks/dur15AdVast';
import { dur30AdVast } from '../../assets/mocks/dur30AdVast';
import { multipleAdsVast } from '../../assets/mocks/multipleAdsVast';
import { Vast4Normalized } from 'adpod-tools';

describe('getAdsBody script test suite', () => {
  test('getAdsBody is function', () => {
    expect(typeof getAdsBody).toEqual('function');
  });

  test('get ad from one ad vast', () => {
    const ad = getAdsBody(oneAdVast as Vast4Normalized);
    expect(ad).toEqual([dur15AdVast]);
  });

  test('get ads from multiple ads vast', () => {
    const adsList = getAdsBody(multipleAdsVast as Vast4Normalized);
    expect(adsList).toEqual([dur15AdVast, dur30AdVast]);
  });
});
