import {
  Channel,
  Country,
  getMarketByChannel,
  LogLevel,
  RedisClusterUpdateStatus,
  UpdateStatus
} from 'adpod-tools';
import logger from '../../libs/logging/logger';
import { Injectable } from '@nestjs/common';
import { ICacheProvider } from '../../libs/caching';
import { IRedisClient } from 'adpod-aws';

type DeapProfiles = {
  default: string[];
  permutive: string[];
};

export type DeapItemType = {
  defaultProfiles: string[];
  permutiveProfiles: string[];
};

export abstract class IDeapProfilesService {
  abstract getDeapProfiles(channel: Channel, uuid?: string): Promise<DeapItemType>;
}

@Injectable()
export class DeapProfileService implements IDeapProfilesService {
  constructor(
    private readonly cacheProvider: ICacheProvider,
    private readonly redisClient: IRedisClient
  ) {}

  public async getDeapProfiles(channel: Channel, uuid?: string): Promise<DeapItemType> {
    if (!uuid) {
      logger('DEAP_PROFILE_NO_UUID', { uuid }, LogLevel.warn);

      return { defaultProfiles: [], permutiveProfiles: [] };
    }

    const status = await this.cacheProvider.get<RedisClusterUpdateStatus>(
      'redisClusterUpdateStatus'
    );

    if (!status) {
      logger('DEAP_CANNOT_FIND_CLUSTER_STATUS', { uuid }, LogLevel.warn);
      return { defaultProfiles: [], permutiveProfiles: [] };
    }
    const country = getMarketByChannel(channel) as Country;
    const redisProfiles = await this.getProfiles(status, country, uuid);

    if (!redisProfiles) {
      logger('DEAP_CANNOT_GET_PROFILES', { uuid }, LogLevel.warn);
      return { defaultProfiles: [], permutiveProfiles: [] };
    }

    return this.extractDefaultAndPermutiveProfiles(redisProfiles, uuid);
  }

  private async extractDefaultAndPermutiveProfiles(redisProfiles: DeapProfiles, uuid: string) {
    let defaultProfiles: string[] = [];
    let permutiveProfiles: string[] = [];

    if (redisProfiles.default && Array.isArray(redisProfiles.default)) {
      defaultProfiles = redisProfiles.default;
    }

    if (redisProfiles.permutive && Array.isArray(redisProfiles.permutive)) {
      permutiveProfiles = redisProfiles.permutive;
    }

    logger(
      'DEAP_UUID_READ_FROM_REDIS',
      { uuid, defaultProfiles, permutiveProfiles },
      LogLevel.dev
    );

    return { defaultProfiles, permutiveProfiles };
  }

  private async getProfiles(
    redisClusterUpdateStatus: RedisClusterUpdateStatus,
    country: Country,
    uuid: string
  ): Promise<DeapProfiles | null> {
    const { status, keyPrefixes } = redisClusterUpdateStatus[country];

    if (status === UpdateStatus.Processing) {
      const [currentKeyPrefix, processingKeyPrefix] = keyPrefixes;
      const redisProfiles = await this.redisClient.get<DeapProfiles>(
        `${country}:{${processingKeyPrefix}}:${uuid}`
      );

      return (
        redisProfiles ??
        (await this.redisClient.get<DeapProfiles>(`${country}:{${currentKeyPrefix}}:${uuid}`))
      );
    }

    const [currentKeyPrefix] = keyPrefixes;
    return this.redisClient.get<DeapProfiles>(`${country}:{${currentKeyPrefix}}:${uuid}`);
  }
}
