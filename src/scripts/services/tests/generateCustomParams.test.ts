import { Test, TestingModule } from '@nestjs/testing';
import { CustomParamsGenerator } from '../customParamsGenerator.service';
import { Channel } from 'adpod-tools';
import { IDeapProfilesService, DeapProfileService } from '../deapProfiles.service';
import { TestCacheModule } from '../../../libs/testing';
import { TestRedisModule } from 'adpod-aws/dist/testing';

describe('generateCustomParams tests', () => {
  let generatorService: CustomParamsGenerator;

  beforeAll(async () => {
    const app: TestingModule = await Test.createTestingModule({
      imports: [TestCacheModule, TestRedisModule],
      providers: [
        CustomParamsGenerator,
        {
          provide: IDeapProfilesService,
          useClass: DeapProfileService
        }
      ]
    }).compile();

    generatorService = app.get(CustomParamsGenerator);
  });

  const custParams = 'param=foo&param2=foo2';

  const version = 'v1_0_0';

  test('should return concatenation of custParams', async () => {
    const res = await generatorService.generate(version, Channel.tvn, custParams);

    expect(res).toEqual('param=foo&param2=foo2&v=v1_0_0');
  });

  test('should return version only', async () => {
    const res = await generatorService.generate(version, Channel.tvn);

    expect(res).toEqual('v=v1_0_0');
  });
});
