import { Injectable } from '@nestjs/common';
import { GoogleAdManagerProvider } from './GoogleAdsManager/googleAdManagerDaiAdsProvider.service';
import { FreeWheelDaiAdsProvider } from './FreeWheel/freeWheelDaiAdsProvider.service';
import { BreakConnector, logger } from 'adpod-tools';
import { DaiAdsProviderFactory } from './daiAdsProviderFactory';
import { AdserverAd, UltimateDaiAdsProviderInput } from '../../../interfaces';
import { AdOceanHandler } from './AdOcean/AdOceanHandler.service';

@Injectable()
export class UltimateDaiAdsProvider {
  private readonly implementedProviders: BreakConnector[];

  constructor(
    private readonly adsFactory: DaiAdsProviderFactory,
    private readonly adOceanHandler: AdOceanHandler,
    private readonly GAMProvider: GoogleAdManagerProvider,
    private readonly FWProvider: FreeWheelDaiAdsProvider
  ) {
    this.implementedProviders = [
      BreakConnector.freeWheelFiller,
      BreakConnector.freeWheelSchedule,
      BreakConnector.gamSchedule,
      BreakConnector.adoceanSlotSchedule,
      BreakConnector.adoceanBreakSchedule
    ];
  }

  public isImplementedProvider(connector: BreakConnector): boolean {
    return this.implementedProviders.includes(connector);
  }

  public async getDaiAds(inputArgs: UltimateDaiAdsProviderInput): Promise<AdserverAd[]> {
    const { providerConnector } = inputArgs;

    const initDataATV = await this.adsFactory.init(inputArgs);

    logger(`CHOSEN PROVIDER: ${providerConnector}`);
    switch (providerConnector) {
      case BreakConnector.freeWheelFiller:
      case BreakConnector.freeWheelSchedule:
        return this.FWProvider.provideAdCoordinator(initDataATV);

      case BreakConnector.gamSchedule:
        return this.GAMProvider.provideAdCoordinator(initDataATV);

      case BreakConnector.adoceanSlotSchedule:
      case BreakConnector.adoceanBreakSchedule:
        return this.adOceanHandler.provideADOAds(inputArgs);

      default:
        throw new Error('Chosen provider is not implemented');
    }
  }
}
