import { Injectable } from '@nestjs/common';
import {
  AdVast4Normalized,
  availableItalyChannels,
  BreakConnector,
  getMediaFileType,
  logger,
  LogLevel,
  RegistryId
} from 'adpod-tools';
import { FillerAdWithDuration, IFillerAdsService } from 'adpod-aws';
import { validators } from '../../../../EnvValidation/envalidConfig';
import { URLParamsHelper } from '../../../adserver/urlHelper';
import { Protocol } from '../../../../models/protocol.model';
import { AdserverAdFreeWheel } from '../../../../interfaces';
import { FillerSlotModelType, TrackingScriptsType } from '../../../../models/filler.model';

export abstract class IFreeWheelFillersAdsProviderService {
  abstract getSufficientDurationAds(
    adServerAd: AdserverAdFreeWheel,
    linearAdDuration: number,
    channel: any,
    breakId: string,
    linearAdPosition: number,
    version: string,
    requestProtocol?: string,
    adId?: string,
    custParams?: string,
    uid?: string
  ): Promise<FillerSlotModelType>;

  abstract isPossibleFwFillerSlot(
    hasMatchingAdServerAd: boolean,
    connector?: BreakConnector,
    hasAdServerAd?: boolean
  ): boolean;
}

@Injectable()
export class FreeWheelFillersAdsProviderService
  implements IFreeWheelFillersAdsProviderService
{
  constructor(private readonly fillersAdsService: IFillerAdsService) {}

  private async getFreeWheelSlotFillers(
    daiAdsDuration: number,
    requiredSlotDuration: number,
    channel: availableItalyChannels
  ): Promise<FillerAdWithDuration[]> {
    const expectedFillersDuration = requiredSlotDuration - daiAdsDuration;

    logger(
      'FW_FILLRS_INIT',
      {
        requiredSlotDuration,
        daiAdsDuration,
        expectedFillersDuration,
        channel
      },
      LogLevel.freewheel
    );

    const fillers = await this.fillersAdsService.getFillersAds(
      channel,
      expectedFillersDuration
    );

    if (fillers.complete) {
      logger('FW_FILLRS_ADS', { fillers }, LogLevel.freewheel);

      return fillers.ads;
    }

    logger('FW_FILLRS_NOT_COMPLETED', { fillers }, LogLevel.freewheel);

    return [];
  }

  private generateTrackingScripts(
    bid: string,
    adBreakPosition: number,
    countadServersAdsForSlot: number,
    mirroredSlotDuration: number,
    fillerDuration: number,
    channel: string,
    mirroredAdId: string,
    version: string,
    requestProtocol: Protocol,
    custParams?: string,
    uid?: string
  ): TrackingScriptsType {
    const url = new URLParamsHelper('', '%2F');

    const createEventCdata = (eventName: string) => {
      url
        .add('p', adBreakPosition)
        .add('bid', bid)
        .add('ch', channel)
        .add('uadid', mirroredAdId, true)
        .add('v', version, true)
        .add('t', 'filler')
        .addMaybe('uid', uid, true)
        .add('dur', mirroredSlotDuration)
        .add('e', eventName)
        .add('bt', 'mirrored')
        .add('m_slot', 'true')
        .add('m_slot_p', countadServersAdsForSlot)
        .add('m_slot_dur', fillerDuration)
        .addMaybe('cust_params', custParams, true)
        .add('prefetch_cap', 'false')
        .add('m', 'IT')
        .addMaybe('app_version', process.env.npm_package_version);

      return `${validators.FILLERS_ADS_HOST.replace('$HTTP_PROTOCOL', requestProtocol)}/?ed=${url.toString()}`;
    };

    return [
      'error',
      'impression',
      'start',
      'firstQuartile',
      'midpoint',
      'thirdQuartile',
      'complete'
    ].reduce((acc, curr) => {
      acc[curr] = createEventCdata(curr);
      return acc;
    }, {} as TrackingScriptsType);
  }

  private generateFillerAdTemplate(
    bid: string,
    adBreakPosition: number,
    fillerDuration: number,
    fillerCreativeId: string,
    fillerMezzanineFileUrl: string,
    trackingScripts: TrackingScriptsType
  ): AdVast4Normalized {
    const mediaType = getMediaFileType(fillerMezzanineFileUrl);

    return {
      _attributes: {
        id: fillerCreativeId,
        campaignId: '',
        sequence: adBreakPosition,
        conditionalAd: false,
        breakId: bid,
        breakType: 'dai'
      },
      InLine: {
        AdSystem: {
          _attributes: { version: '4.0' },
          _text: 'Filler Server IT'
        },
        AdTitle: { _text: 'Filler Ad' },
        Creatives: {
          Creative: [
            {
              _attributes: {
                id: fillerCreativeId
              },
              UniversalAdId: {
                _attributes: { idRegistry: RegistryId.di },
                _text: fillerCreativeId
              },
              Linear: {
                Duration: {
                  _text: new Date(fillerDuration * 1000).toISOString().slice(11, 19)
                },
                TrackingEvents: {
                  Tracking: [
                    { _attributes: { event: 'start' }, _cdata: trackingScripts.start },
                    {
                      _attributes: { event: 'firstQuartile' },
                      _cdata: trackingScripts.firstQuartile
                    },
                    { _attributes: { event: 'midpoint' }, _cdata: trackingScripts.midpoint },
                    {
                      _attributes: { event: 'thirdQuartile' },
                      _cdata: trackingScripts.thirdQuartile
                    },
                    { _attributes: { event: 'complete' }, _cdata: trackingScripts.complete }
                  ]
                },
                MediaFiles: {
                  MediaFile: [
                    {
                      _attributes: {
                        delivery: 'progressive',
                        type: mediaType,
                        width: 1920,
                        height: 1080
                      },
                      _cdata: fillerMezzanineFileUrl
                    }
                  ],
                  Mezzanine: {
                    _attributes: {
                      delivery: 'progressive',
                      type: mediaType,
                      width: 1920,
                      height: 1080
                    },
                    _cdata: fillerMezzanineFileUrl
                  }
                }
              }
            }
          ]
        },
        Impression: [
          {
            _attributes: { id: '' },
            _cdata: trackingScripts.impression
          }
        ],
        Error: [
          {
            _attributes: { id: '' },
            _cdata: trackingScripts.error
          }
        ]
      }
    };
  }

  public async getSufficientDurationAds(
    adServerAd: AdserverAdFreeWheel,
    linearAdDuration: number,
    channel: any,
    breakId: string,
    linearAdPosition: number,
    version: string,
    requestProtocol: Protocol,
    adId?: string,
    custParams?: string,
    uid?: string
  ): Promise<FillerSlotModelType> {
    const { daiAdsDuration, vast } = adServerAd;
    const countAdServerAds = vast.length;

    const fillersAdsTemplates: AdVast4Normalized[] = [];
    const freewheelAds: AdVast4Normalized[] = [];

    if (daiAdsDuration && daiAdsDuration === linearAdDuration) {
      return { freewheelAds: adServerAd.vast, fillersAdsTemplates };
    }

    if (!(daiAdsDuration && daiAdsDuration < linearAdDuration)) {
      return { freewheelAds, fillersAdsTemplates };
    }

    const fillersAds = await this.getFreeWheelSlotFillers(
      daiAdsDuration,
      linearAdDuration,
      channel
    );

    if (!fillersAds.length) {
      return { freewheelAds, fillersAdsTemplates };
    }

    fillersAds.forEach((fillerAd: FillerAdWithDuration) => {
      const adPositionInSlot = countAdServerAds + 1;

      const { fid: fillerId, duration: fillerDuration, url: fillerUrl } = fillerAd;

      const trackingScripts: TrackingScriptsType = this.generateTrackingScripts(
        breakId,
        linearAdPosition,
        adPositionInSlot,
        linearAdDuration,
        fillerDuration,
        channel,
        adId!,
        version,
        requestProtocol,
        custParams,
        uid
      );

      fillersAdsTemplates.push(
        this.generateFillerAdTemplate(
          breakId,
          linearAdPosition,
          fillerDuration,
          fillerId,
          fillerUrl,
          trackingScripts
        )
      );
    });

    return { freewheelAds: adServerAd.vast, fillersAdsTemplates };
  }

  public isPossibleFwFillerSlot(
    hasMatchingAdServerAd: boolean,
    connector?: BreakConnector,
    hasAdServerAd?: boolean
  ): boolean {
    return (
      connector === BreakConnector.freeWheelSchedule &&
      !hasMatchingAdServerAd &&
      !!hasAdServerAd
    );
  }
}
