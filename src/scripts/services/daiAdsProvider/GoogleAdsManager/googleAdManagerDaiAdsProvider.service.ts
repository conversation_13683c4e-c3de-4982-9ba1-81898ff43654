import { Injectable } from '@nestjs/common';
import {
  xmlParser,
  LogLevel,
  request,
  Vast4,
  isObject,
  normalizeVast4,
  AdVast4Normalized,
  BreakConnector,
  returnAsArray,
  RegistryId,
  logger
} from 'adpod-tools';
import {
  AdserverAdGAM,
  AdserverAdPreParse,
  DaiAdsProviderAtvSpotsType,
  DaiAdsProviderOutput,
  fetchAdsOutput,
  logRequestStatsInputATV,
  VastGam,
  DaiAdsRequestCommon,
  AdVastGam,
  Extension
} from '../../../../interfaces';
import { prepareGAMAgent } from './agentPreparator';
import { validators } from '../../../../EnvValidation/envalidConfig';
import { VastTrackings } from '../../../../models/vast4.model';
import { WorkerConfigType } from '../../../../models/workerConfig';
import { EnhancedVastCountryEnum } from '../../../../models/enhancedVastCountry.model';
import { IDebugService } from '../../../../libs/caching/services/debug.service';
import { isEmptyVast } from '../../../vast/isEmptyVast';
import { ICacheProvider } from '../../../../libs/caching';
import { DaiAdsProvider_REST } from '../abstractDaiAdsProvider';
import {
  formatPerformanceTime,
  getCurrentPerformanceTime
} from '../../../utils/performanceTime';
import { createGAMRequestUrl } from '../../../adserver/createGAMRequestUrl';

@Injectable()
export class GoogleAdManagerProvider extends DaiAdsProvider_REST {
  constructor(
    private readonly localCache: ICacheProvider,
    private readonly debugService: IDebugService
  ) {
    super();
  }

  public async provideAdCoordinator(
    inputArgs: DaiAdsProviderOutput
  ): Promise<AdserverAdGAM[]> {
    const { daiAds, startFetchTime, endFetchTime } = await this.fetchAds(inputArgs);

    const daiAdsExtractedData = await this.extractDataFromDaiAds(daiAds, inputArgs);

    const { atvSpots, version, channel, connector } = inputArgs.config;

    const filledSlots = daiAdsExtractedData.filter(({ vast }) => vast.length > 0).length;

    this.logAdServerRequestStats({
      startFetchTime,
      endFetchTime,
      requestedSlots: atvSpots.length,
      returnedSlots: daiAdsExtractedData.length,
      filledSlots,
      version,
      connector,
      channel,
      isProgrammatic: daiAdsExtractedData.some(({ isProgrammaticAd }) => isProgrammaticAd)
    });

    if (filledSlots === 0) {
      return daiAdsExtractedData;
    }

    return this.handleExtractedData(atvSpots, daiAdsExtractedData, version);
  }

  private async handleExtractedData(
    atvSpots: DaiAdsProviderAtvSpotsType[],
    daiAds: AdserverAdGAM[],
    version: string
  ): Promise<AdserverAdGAM[]> {
    const noProgrammaticHandler = this.fillNoProgrammaticClosure(atvSpots);

    const updatedDaiAdsPromises = daiAds.map(async (el) => {
      if (el.isProgrammaticAd) {
        const adWithReplacedAdId = this.replaceDaiAdsCreativeId(el);

        const adWithUniversalTag = this.addUniversalAdIdTag(adWithReplacedAdId);

        const addWithSourceAttr = this.addSourceAttr(adWithUniversalTag);
        return await this.replaceProgrammaticDaiAdsMediaFileTag(addWithSourceAttr, version);
      }
      return noProgrammaticHandler(el);
    });

    const resolvedDaiAds = await Promise.all(updatedDaiAdsPromises);

    return resolvedDaiAds.filter((el) => !!el) as AdserverAdGAM[];
  }

  protected async fetchAds(inputArgs: DaiAdsProviderOutput): Promise<fetchAdsOutput> {
    const startFetchTime = getCurrentPerformanceTime();

    const allResponses: AdserverAdPreParse[] = await Promise.all(
      inputArgs.config.atvSpots.map((spot) =>
        this.requestAd(
          spot,
          inputArgs.request,
          inputArgs.config.breakId,
          inputArgs.config.version,
          inputArgs.config.connector
        )
      )
    ).catch((error) => {
      logger('ERR_HANDLING_ADSERVER_VASTS', { error });
      return [];
    });

    const endFetchTime = getCurrentPerformanceTime();

    const daiAds = allResponses.filter(({ vast }) => !!vast);

    return { startFetchTime, endFetchTime, daiAds };
  }

  protected logAdServerRequestStats(args: logRequestStatsInputATV): void {
    const {
      startFetchTime,
      endFetchTime,
      requestedSlots,
      returnedSlots,
      filledSlots,
      version,
      connector,
      channel,
      isProgrammatic
    } = args;
    const processingTime = +formatPerformanceTime(startFetchTime, endFetchTime);

    const processingTimeRounded = Math.round(processingTime / 10);

    logger(
      `STATS_ADSERVER_GAM_PTR_${processingTimeRounded}_REQUSTEDSLOTS_${requestedSlots}_RETURNEDSLOTS_${returnedSlots}_FILLEDSLOTS_${filledSlots}_V_${version}_CONNECTOR_${connector}_CHANNEL_${channel}`,
      {
        requestedSlots,
        returnedSlots,
        filledSlots,
        processingTime,
        processingTimeRounded,
        version,
        connector,
        channel,
        isProgrammatic
      },
      processingTime > validators.LOG_ADSERVER_RESPONSE_PROCESSING_TIME_WARN_THRESHOLD
        ? LogLevel.warn
        : LogLevel.statsAdserverGam
    );
  }

  async requestAd(
    spot: DaiAdsProviderAtvSpotsType,
    providerRequest: DaiAdsRequestCommon,
    breakId: string,
    version: string,
    connector: BreakConnector
  ): Promise<AdserverAdPreParse> {
    const { adrequest, position } = spot;
    const { ip, ua } = providerRequest;

    const adServerUrl = createGAMRequestUrl(adrequest, providerRequest);

    logger('FETCH_AD', { adServerUrl, connector });

    const response: AdserverAdPreParse = {
      vast: null,
      position: position ? +position : null,
      adServerUrl,
      breakId,
      connector,
      isReplaced: false
    };

    try {
      const headers = {
        'user-agent': ua,
        'X-Forwarded-For': ip
      };
      logger('GAM_ADSERVER_REQUEST_HEADERS', { headers }, LogLevel.gam);

      const resData = await request(adServerUrl, {
        headers,
        credentials: 'include',
        agent: prepareGAMAgent()
      });

      const vast = (await resData.text()) as string;
      response.vast = vast;
      response.isReplaced = !isEmptyVast(vast);
    } catch (err) {
      logger('ERROR_FETCH_AD', { err }, LogLevel.error);
    }

    await this.debugService.setSpotDebugDetails({
      breakId,
      position,
      adServerUrl,
      version,
      connector
    });

    return response;
  }

  protected async parseResponse(jsonVast: object | null): Promise<AdVast4Normalized[]> {
    if (!jsonVast) {
      return [];
    }

    return this.handleAdDataTag(jsonVast as Vast4);
  }

  protected async getVasts(
    jsonVast: VastGam | null,
    inputArgs: DaiAdsProviderOutput
  ): Promise<{
    programmaticVast: Vast4 | null;
    firstVast: VastGam | null;
  }> {
    let programmaticVast: Vast4 | null = null;
    let firstVast: VastGam | null = null;

    if (!jsonVast) {
      return {
        programmaticVast,
        firstVast
      };
    }

    firstVast = jsonVast;

    if (this.isProgrammaticResponse(jsonVast)) {
      const intermediateURL = jsonVast.VAST.Ad?.Wrapper.VASTAdTagURI._cdata;
      try {
        const headers = {
          'user-agent': inputArgs.request.ua,
          'X-Forwarded-For': inputArgs.request.ip
        };

        logger('GAM_INTERMEDIATE_VAST_REQUEST_HEADERS', { headers }, LogLevel.gam);

        const response = await request(intermediateURL, { headers });
        const text = await response.text();

        programmaticVast = xmlParser.fromXMLtoJSON(text) as Vast4 | null;
      } catch (error) {
        logger('ERROR_GAM_INTERMEDIATE_VAST', { error }, LogLevel.error);
        return {
          programmaticVast,
          firstVast
        };
      }
    }

    return {
      programmaticVast,
      firstVast
    };
  }

  protected async getVastsTrackings(
    programmaticVast: Vast4 | null,
    firstVast: VastGam | Vast4 | null
  ): Promise<{
    firstVastTrackings: VastTrackings;
    programmaticVastTrackings: VastTrackings;
  }> {
    let programmaticVastTrackings: VastTrackings = {
      trackingEvents: [],
      impressionEvents: [],
      errorEvents: []
    };

    let firstVastTrackings: VastTrackings = {
      trackingEvents: [],
      impressionEvents: [],
      errorEvents: []
    };

    if (programmaticVast) {
      const programmaticVastNormalized = normalizeVast4(programmaticVast);
      const hasInlineTag = programmaticVastNormalized.VAST.Ad[0].InLine;

      if (hasInlineTag) {
        programmaticVastTrackings = {
          trackingEvents: returnAsArray(
            programmaticVastNormalized.VAST.Ad[0].InLine!.Creatives.Creative[0].Linear!
              .TrackingEvents.Tracking
          ),
          impressionEvents: returnAsArray(
            programmaticVastNormalized.VAST.Ad[0].InLine!.Impression
          ),
          errorEvents: returnAsArray(programmaticVastNormalized.VAST.Ad[0].InLine!.Error)
        };
      }
    }

    if (firstVast) {
      const wrapper = (firstVast.VAST!.Ad as AdVastGam)?.Wrapper;

      if (wrapper) {
        firstVastTrackings = {
          trackingEvents: returnAsArray(
            wrapper!.Creatives.Creative.Linear!.TrackingEvents.Tracking
          ),
          impressionEvents: returnAsArray(wrapper!.Impression),
          errorEvents: returnAsArray(wrapper!.Error)
        };
      } else {
        const vast4Normalized = normalizeVast4(firstVast as Vast4);

        const vast4AdInline = vast4Normalized.VAST.Ad[0]?.InLine;

        if (vast4AdInline) {
          firstVastTrackings = {
            trackingEvents:
              vast4AdInline.Creatives.Creative[0].Linear!.TrackingEvents.Tracking,
            impressionEvents: vast4AdInline.Impression,
            errorEvents: vast4AdInline.Error
          };
        }
      }
    }

    return { firstVastTrackings, programmaticVastTrackings };
  }

  protected async updateAdTrackings(
    adData: AdVast4Normalized[],
    firstVastTrackings: VastTrackings,
    programmaticVastTrackings?: VastTrackings
  ): Promise<AdVast4Normalized[]> {
    const adDataUpdated: AdVast4Normalized[] = adData;

    if (!adDataUpdated[0]) {
      return adData;
    }

    if (firstVastTrackings.trackingEvents) {
      adDataUpdated[0].InLine!.Creatives.Creative[0].Linear!.TrackingEvents.Tracking =
        firstVastTrackings.trackingEvents;
    }

    if (firstVastTrackings.impressionEvents) {
      adDataUpdated[0].InLine!.Impression = firstVastTrackings.impressionEvents;
    }

    if (firstVastTrackings.errorEvents) {
      adDataUpdated[0].InLine!.Error = firstVastTrackings.errorEvents;
    }

    const currentTrackings =
      adDataUpdated[0].InLine!.Creatives.Creative[0].Linear!.TrackingEvents.Tracking;
    const currentImpressions = adDataUpdated[0].InLine!.Impression;
    const currentErrors = adDataUpdated[0].InLine!.Error;

    if (programmaticVastTrackings) {
      adDataUpdated[0].InLine!.Creatives.Creative[0].Linear!.TrackingEvents.Tracking = [
        ...currentTrackings,
        ...programmaticVastTrackings.trackingEvents
      ];

      adDataUpdated[0].InLine!.Impression = [
        ...currentImpressions,
        ...programmaticVastTrackings.impressionEvents
      ];

      adDataUpdated[0].InLine!.Error = [
        ...currentErrors,
        ...programmaticVastTrackings.errorEvents
      ];
    }

    return adDataUpdated;
  }

  protected async updateAdExtensions(
    adData: AdVast4Normalized[],
    firstVast: VastGam | null
  ): Promise<AdVast4Normalized[]> {
    const updatedAdData = adData;

    const currentExtensions = adData[0]?.InLine?.Extensions?.Extension;
    const gamExtensions = firstVast?.VAST.Ad?.Wrapper?.Extensions?.Extension;

    if (!(currentExtensions && gamExtensions)) {
      return adData;
    }

    const gamExtensionsWithoutAttributionUrl = this.removeAttributionUrl(gamExtensions);

    updatedAdData[0].InLine!.Extensions!.Extension = [
      ...currentExtensions,
      ...gamExtensionsWithoutAttributionUrl
    ];

    return updatedAdData;
  }

  private removeAttributionUrl(extensions: Extension[]) {
    return extensions.map((extension) => {
      const params = extension.UI?.config.params;
      if (params?.attribution_url) {
        delete params.attribution_url;
      }

      return extension;
    });
  }

  protected async extractDataFromDaiAds(
    daiAds: AdserverAdPreParse[],
    inputArgs: DaiAdsProviderOutput
  ): Promise<AdserverAdGAM[]> {
    const daiAdsExtractedData = await Promise.all(
      daiAds.map(async (res: AdserverAdPreParse): Promise<AdserverAdGAM> => {
        const jsonVast = xmlParser.fromXMLtoJSON(res.vast) as VastGam | null;

        const { programmaticVast, firstVast } = await this.getVasts(jsonVast, inputArgs);

        const { firstVastTrackings, programmaticVastTrackings } = await this.getVastsTrackings(
          programmaticVast,
          firstVast
        );

        const adData = await this.parseResponse(programmaticVast ?? firstVast);

        const adDataWithUpdatedTrackings = await this.updateAdTrackings(
          adData,
          firstVastTrackings,
          programmaticVastTrackings
        );

        const adDataWithUpdatedExtensions = await this.updateAdExtensions(
          adDataWithUpdatedTrackings,
          firstVast
        );

        let isProgrammaticAd = false;
        let universalAdId: string | null = null;
        if (adDataWithUpdatedExtensions.length > 0) {
          isProgrammaticAd = this.isProgrammaticTypeAd(adDataWithUpdatedExtensions);

          if (isProgrammaticAd) {
            universalAdId = this.getUniversalAdIdValue(adDataWithUpdatedExtensions);
          }
        }

        return {
          ...res,
          vast: adDataWithUpdatedExtensions,
          isProgrammaticAd,
          universalAdId
        };
      })
    );

    return daiAdsExtractedData;
  }

  private handleAdDataTag(jsonVast: Vast4 | null): AdVast4Normalized[] {
    logger('GAM_VAST', { vast: jsonVast }, LogLevel.debug);

    if (!jsonVast) {
      return [];
    }

    const vast4Normalized = normalizeVast4(jsonVast);

    return vast4Normalized.VAST.Ad;
  }

  private addSourceAttr(adserverAd: AdserverAdGAM): AdserverAdGAM {
    const ad = adserverAd;

    ad.vast[0]._attributes = {
      ...ad.vast[0]._attributes,
      source: 'programmatic'
    };

    return ad;
  }

  private isProgrammaticTypeAd(adData: AdVast4Normalized[]): boolean {
    return (adData[0].InLine?.AdSystem._text ?? '') === 'DBM';
  }

  private replaceDaiAdsCreativeId(el: AdserverAdGAM): AdserverAdGAM {
    if (!el.universalAdId) {
      return el;
    }

    const creativeAttributes = el.vast[0]?.InLine?.Creatives?.Creative[0]?._attributes;

    if (creativeAttributes) {
      if (creativeAttributes?.AdID) {
        delete creativeAttributes.AdID;
      }

      creativeAttributes.id = el.universalAdId;
    }

    return el;
  }

  private addUniversalAdIdTag(el: AdserverAdGAM): AdserverAdGAM {
    if (!el.universalAdId || !el.vast[0]?.InLine?.Creatives) {
      return el;
    }

    const creativeTag = el.vast[0]?.InLine?.Creatives?.Creative[0];

    if (creativeTag) {
      el.vast[0].InLine.Creatives = {
        Creative: [
          {
            UniversalAdId: {
              _text: el.universalAdId,
              _attributes: {
                idRegistry: RegistryId.tvn
              }
            },
            ...creativeTag
          }
        ]
      };
    }

    return el;
  }

  private getUniversalAdIdValue(adVast: AdVast4Normalized[]): string | null {
    return (
      adVast[0].InLine?.Extensions?.Extension.find(
        (extension) => extension._attributes?.type === 'GDCM_3RD_PARTY'
      )?.UniversalAdId?.Id?._text ?? null
    );
  }

  private async replaceProgrammaticDaiAdsMediaFileTag(
    el: AdserverAdGAM,
    version: string
  ): Promise<AdserverAdGAM> {
    if (!(el.vast.length === 1 && el.isProgrammaticAd && el.universalAdId)) {
      return el;
    }

    const linearTag = el.vast[0].InLine?.Creatives?.Creative[0]?.Linear;
    if (!linearTag) {
      return el;
    }

    const workerConfig = await this.localCache.get<WorkerConfigType>('workerConfig');
    const enchancedVastPlVersions = workerConfig?.programmaticEnchancedVastPlversions;

    const enhancedVastCountry = enchancedVastPlVersions?.includes(version)
      ? EnhancedVastCountryEnum.pl
      : EnhancedVastCountryEnum.it;

    const enhancedVast = await this.fetchMediaEnhancedVast(
      el.universalAdId,
      enhancedVastCountry
    );
    if (!enhancedVast) {
      return el;
    }

    const enhancedVastJson = xmlParser.fromXMLtoJSON(enhancedVast) as Vast4;
    const vast4Normalized = normalizeVast4(enhancedVastJson);
    logger('ENHANCED_VAST_JSON', { json: vast4Normalized }, LogLevel.debug);

    const enhancedVastMediaFiles =
      vast4Normalized.VAST.Ad[0].InLine?.Creatives?.Creative[0]?.Linear?.MediaFiles;

    const enhancedVastMediaFileArray = enhancedVastMediaFiles?.MediaFile;
    if (!enhancedVastMediaFileArray) {
      return el;
    }

    linearTag.MediaFiles = {
      ...enhancedVastMediaFiles,
      MediaFile: [
        {
          _attributes: { type: 'video/mxf' },
          _cdata: enhancedVastMediaFileArray[0]?._cdata
        }
      ],
      Mezzanine: {
        _cdata: enhancedVastMediaFileArray[0]?._cdata
      }
    };

    return el;
  }

  private fillNoProgrammaticClosure(
    atvSpots: DaiAdsProviderAtvSpotsType[]
  ): (res: AdserverAdGAM) => AdserverAdGAM | null {
    const linearConfigAds = atvSpots
      .map(({ adId }) => adId)
      .filter((spot) => !!spot) as string[]; // does all atv Ads have adId?

    const getNonlinearConfigAd = (res: AdserverAdGAM): boolean => {
      if (res.vast.length === 0) {
        return true;
      }

      const adId = res.vast[0].InLine?.Creatives.Creative[0]?._attributes?.id;

      return !adId || !linearConfigAds.includes(adId);
    };

    return (res: AdserverAdGAM): AdserverAdGAM | null => {
      return getNonlinearConfigAd(res) ? res : null;
    };
  }

  isProgrammaticResponse = (jsonVast: object | null): jsonVast is VastGam => {
    return (
      !!jsonVast &&
      'VAST' in jsonVast &&
      isObject(jsonVast.VAST) &&
      'Ad' in jsonVast.VAST &&
      isObject(jsonVast.VAST.Ad) &&
      'Wrapper' in jsonVast.VAST.Ad
    );
  };
}
