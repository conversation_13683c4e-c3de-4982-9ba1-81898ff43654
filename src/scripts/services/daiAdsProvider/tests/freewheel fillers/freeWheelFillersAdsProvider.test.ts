import { Test, TestingModule } from '@nestjs/testing';

import {
  FreeWheelFillersAdsProviderService,
  IFreeWheelFillersAdsProviderService
} from '../../FreeWheel/freeWheelFillersAdsProvider.service';
import { FillerAdsService, IFillerAdsService } from 'adpod-aws';
import { BreakConnector } from 'adpod-tools';
import { TestRedisModule } from 'adpod-aws/dist/testing';

describe('FreeWheelFillers service test suite', () => {
  let service: IFreeWheelFillersAdsProviderService;

  beforeAll(async () => {
    const app: TestingModule = await Test.createTestingModule({
      imports: [TestRedisModule],
      providers: [
        FreeWheelFillersAdsProviderService,
        {
          provide: IFillerAdsService,
          useClass: FillerAdsService
        }
      ]
    }).compile();

    service = app.get(FreeWheelFillersAdsProviderService);
  });

  describe('FreeWheelFillers isPossibleFwFillerSlot  test suite', () => {
    it('should enable fillers for FW response containing VAST & not matched ad duration', async () => {
      const result = service.isPossibleFwFillerSlot(
        false,
        BreakConnector.freeWheelSchedule,
        true
      );
      expect(result).toBeTruthy();
    });

    it("shouldn't enable fillers for FW response containing VAST & matched ad duration", async () => {
      const result = service.isPossibleFwFillerSlot(
        true,
        BreakConnector.freeWheelSchedule,
        true
      );
      expect(result).toBeFalsy();
    });
  });
});
