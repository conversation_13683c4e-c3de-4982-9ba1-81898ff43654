import { Test, TestingModule } from '@nestjs/testing';
import { UltimateDaiAdsProvider } from '../UltimateDaiAdsProvider.service';
import { BreakConnector } from 'adpod-tools';
import { DaiAdsProviderFactory } from '../daiAdsProviderFactory';
import { AdOceanHandler } from '../AdOcean/AdOceanHandler.service';
import { GoogleAdManagerProvider } from '../GoogleAdsManager/googleAdManagerDaiAdsProvider.service';
import { FreeWheelDaiAdsProvider } from '../FreeWheel/freeWheelDaiAdsProvider.service';

describe('UltimateDaiAdsProvider test suite', () => {
  const AdOceanHandlerSpy = jest.fn();
  const GoogleAdManagerProviderSpy = jest.fn();
  const FreeWheelDaiAdsProviderSpy = jest.fn();
  const DaiAdsProviderFactorySpy = jest.fn();

  let service: UltimateDaiAdsProvider;

  beforeAll(async () => {
    const app: TestingModule = await Test.createTestingModule({
      providers: [
        UltimateDaiAdsProvider,
        { provide: DaiAdsProviderFactory, useValue: { init: DaiAdsProviderFactorySpy } },
        { provide: AdOceanHandler, useValue: { provideADOAds: AdOceanHandlerSpy } },
        {
          provide: GoogleAdManagerProvider,
          useValue: { provideAdCoordinator: GoogleAdManagerProviderSpy }
        },
        {
          provide: FreeWheelDaiAdsProvider,
          useValue: { provideAdCoordinator: FreeWheelDaiAdsProviderSpy }
        }
      ]
    }).compile();

    service = app.get(UltimateDaiAdsProvider);
  });

  afterEach(() => {
    jest.resetAllMocks();
    jest.clearAllMocks();
  });

  describe('implementedProviders', () => {
    it('should return true', () => {
      expect(service.isImplementedProvider(BreakConnector.freeWheelFiller)).toBe(true);
      expect(service.isImplementedProvider(BreakConnector.freeWheelSchedule)).toBe(true);
      expect(service.isImplementedProvider(BreakConnector.gamSchedule)).toBe(true);
      expect(service.isImplementedProvider(BreakConnector.adoceanBreakSchedule)).toBe(true);
      expect(service.isImplementedProvider(BreakConnector.adoceanSlotSchedule)).toBe(true);
    });

    it('should return false', () => {
      expect(service.isImplementedProvider(BreakConnector.freeWheelDuration)).toBe(false);
      expect(service.isImplementedProvider(BreakConnector.none)).toBe(false);
    });
  });

  describe('getDaiAds - should call appropriate provider', () => {
    it('Connector.adoceanProxy', async () => {
      await service.getDaiAds({
        providerConnector: BreakConnector.adoceanSlotSchedule
      } as any);
      expect(AdOceanHandlerSpy).toHaveBeenCalled();
    });

    it('Connector.adoceanBreak', async () => {
      await service.getDaiAds({
        providerConnector: BreakConnector.adoceanBreakSchedule
      } as any);
      expect(AdOceanHandlerSpy).toHaveBeenCalled();
    });

    it('Connector.freeWheelFiller', async () => {
      await service.getDaiAds({
        providerConnector: BreakConnector.freeWheelFiller
      } as any);
      expect(FreeWheelDaiAdsProviderSpy).toHaveBeenCalled();
    });

    it('Connector.freeWheelDi', async () => {
      await service.getDaiAds({
        providerConnector: BreakConnector.freeWheelSchedule
      } as any);
      expect(FreeWheelDaiAdsProviderSpy).toHaveBeenCalled();
    });

    it('Connector.gam', async () => {
      await service.getDaiAds({
        providerConnector: BreakConnector.gamSchedule
      } as any);
      expect(GoogleAdManagerProviderSpy).toHaveBeenCalled();
    });

    it('Not implemented Connector', async () => {
      expect(async () => {
        return service.getDaiAds({
          providerConnector: BreakConnector.freeWheelDuration
        } as any);
      }).rejects.toThrow('Chosen provider is not implemented');

      expect(async () => {
        return service.getDaiAds({ providerConnector: BreakConnector.none } as any);
      }).rejects.toThrow('Chosen provider is not implemented');
    });
  });
});
