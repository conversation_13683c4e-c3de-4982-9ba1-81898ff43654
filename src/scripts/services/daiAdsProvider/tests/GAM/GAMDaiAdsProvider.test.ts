import * as Request from 'adpod-tools/dist/utils/fetch/request';
import { DaiAdsProviderOutput } from '../../../../../interfaces';
import { GoogleAdManagerProvider } from '../../GoogleAdsManager/googleAdManagerDaiAdsProvider.service';
import https from 'https';
import * as prepareGAMAgentFile from '../../GoogleAdsManager/agentPreparator';
import { outputData1 } from '../factory/daiAdsProviderFactory.output';
import {
  initialResponseSuccess,
  intermediateResponse,
  enhancedVast,
  initialResponseFailure,
  adBase,
  atvSpots
} from './gamDaiAdsProvider.mockData';
import { TestingModule } from '@nestjs/testing/testing-module';
import { Test } from '@nestjs/testing';
import { DebugService, IDebugService } from '../../../../../libs/caching';
import { TestCacheModule } from '../../../../../libs/testing';

describe('googleAdManager service test suite', () => {
  let service: GoogleAdManagerProvider;

  beforeAll(async () => {
    const app: TestingModule = await Test.createTestingModule({
      imports: [TestCacheModule],
      providers: [
        GoogleAdManagerProvider,
        {
          provide: IDebugService,
          useClass: DebugService
        }
      ]
    }).compile();

    service = app.get(GoogleAdManagerProvider);
  });

  beforeEach(() => {
    jest.spyOn(prepareGAMAgentFile, 'prepareGAMAgent').mockReturnValue({} as https.Agent);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.resetAllMocks();
  });

  it('should return empty vast cause request was not successful', async () => {
    const requestSpy = jest.spyOn(Request, 'request');
    requestSpy.mockRejectedValue({ text: () => null });

    const response = await service.requestAd(
      atvSpots[0],
      outputData1.request,
      outputData1.config.breakId,
      outputData1.config.version,
      outputData1.config.connector
    );
    expect(response).toEqual(adBase);
  });

  it('should return fulfilled vast with mocked request', async () => {
    jest.spyOn(Request, 'request').mockResolvedValue({ text: () => 'Vast as string' });

    const response = await service.requestAd(
      atvSpots[0],
      outputData1.request,
      outputData1.config.breakId,
      outputData1.config.version,
      outputData1.config.connector
    );

    expect(response).toEqual({
      ...adBase,
      vast: 'Vast as string',
      isReplaced: false
    });
  });

  it('should provide Ad Coordinator', async () => {
    const requestSpy = jest.spyOn(Request, 'request');

    requestSpy
      .mockResolvedValueOnce(initialResponseFailure)
      .mockResolvedValueOnce(initialResponseFailure)
      .mockResolvedValueOnce(initialResponseSuccess)
      .mockResolvedValueOnce(intermediateResponse)
      .mockResolvedValueOnce(enhancedVast)
      .mockResolvedValue(new Error());

    const adCoordinator = await service.provideAdCoordinator(
      outputData1 as DaiAdsProviderOutput
    );

    expect(requestSpy).toHaveBeenCalledTimes(5);
    expect(adCoordinator).toMatchSnapshot();
  });
});
