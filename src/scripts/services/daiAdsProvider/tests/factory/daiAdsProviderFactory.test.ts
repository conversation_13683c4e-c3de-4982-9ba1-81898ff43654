import { Test, TestingModule } from '@nestjs/testing';
import { DaiAdsProviderFactory } from '../../daiAdsProviderFactory';
import { CustomParamsGenerator } from '../../../customParamsGenerator.service';
import { initData, initData2 } from './daiAdsProviderFactory.input';
import { IDeapProfilesService, DeapProfileService } from '../../../deapProfiles.service';
import { TcfService } from '../../../tcf.service';
import { FreeWheelFillersAdsProviderService } from '../../FreeWheel/freeWheelFillersAdsProvider.service';
import { FillerAdsService, IFillerAdsService } from 'adpod-aws';
import { TestCacheModule } from '../../../../../libs/testing';
import { TestRedisModule } from 'adpod-aws/dist/testing';

describe('daiAdsProviderFactory test suite', () => {
  let factory: DaiAdsProviderFactory;

  beforeAll(async () => {
    const app: TestingModule = await Test.createTestingModule({
      imports: [TestCacheModule, TestRedisModule],
      providers: [
        DaiAdsProviderFactory,
        CustomParamsGenerator,
        TcfService,
        FreeWheelFillersAdsProviderService,
        {
          provide: IFillerAdsService,
          useClass: FillerAdsService
        },
        {
          provide: IDeapProfilesService,
          useClass: DeapProfileService
        }
      ]
    }).compile();

    factory = app.get(DaiAdsProviderFactory);
  });

  it('should initialize data', async () => {
    const result = await factory.init(initData as any);
    expect(result).toMatchSnapshot();
  });

  it('should initialize data ado break', async () => {
    const result = await factory.initBreak(initData2 as any);
    expect(result).toMatchSnapshot();
  });
});
