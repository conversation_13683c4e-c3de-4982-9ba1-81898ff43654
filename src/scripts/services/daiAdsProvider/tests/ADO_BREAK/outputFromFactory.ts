import { AdType, BreakConnector } from 'adpod-tools';
import { DaiAdsProviderOutputBreak } from '../../../../../interfaces';

export const outputData: DaiAdsProviderOutputBreak = {
  request: {
    headers: {
      'x-device-ip': '*************',
      'x-device-user-agent':
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
      'user-agent': 'PostmanRuntime/7.39.0'
    },
    ip: '*************',
    ua: [
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36'
    ],
    gdpr: undefined,
    gdprConsent: undefined,
    custParams: 'TEST_AUTO=true&v=v1_0_0_dai50_WC_AB',
    uid: undefined
  },
  config: {
    breakId: '7515751533714321',
    blockDuration: 300,
    version: 'v1_0_0_dai50_WC_AB',
    channel: 'TVN',
    breakAdRequest:
      'http://tvn.adocean.pl/ad.xml?aocodetype=1/id=A1w3VxpUJ7UcI7sa6HfkBq7OAyJJ31IAAF6sV.rSP2L.S7/ch=TVN/bid=7515751533714321/ct=linear/adid=r229291.mov_r236051.mov_r236239.mov_r235494.mov_r236278.mov_r217694.mov/mid2mindur=30/mid2dur=30/mid2maxdur=30/mid3mindur=15/mid3dur=15/mid3maxdur=15/mid5mindur=30/mid5dur=30/mid5maxdur=30/mid6mindur=30/mid6dur=30/mid6maxdur=30/mid8mindur=15/mid8dur=15/mid8maxdur=15/mid10mindur=15/mid10dur=15/mid10maxdur=15/tvn_restriction_labels=category_263641466_TELEKOMUNIKACJA-INTERNET%2Cexclude_category_263641466_TELEKOMUNIKACJA-INTERNET%2Ccategory_449732376_AFLO_MAXICORTAN%2Cexclude_category_449732376_AFLO_MAXICORTAN%2Ccategory_258675300_USLUGI_BUKMACHERSKIE%2Cexclude_category_258675300_USLUGI_BUKMACHERSKIE%2Ccategory_449287917_USP_SUPLEMENTY_DIETY%2Cexclude_category_449287917_USP_SUPLEMENTY_DIETY%2Ccategory_18_MLEKO_PROD._MLECZNE%2Ctype_HFSS%2Cexclude_category_18_MLEKO_PROD._MLECZNE',
    breakConnector: BreakConnector.adoceanBreakSchedule,
    adUnitId: 'A1w3VxpUJ7UcI7sa6HfkBq7OAyJJ31IAAF6sV.rSP2L.S7',
    exactLength: false,
    spots: [
      {
        type: AdType.atv,
        position: 2,
        duration: 30,
        adId: '',
        slotRestrictions: []
      },
      {
        type: AdType.atv,
        position: 3,
        duration: 15,
        adId: '',
        slotRestrictions: []
      },
      {
        type: AdType.atv,
        position: 5,
        duration: 30,
        adId: '',
        slotRestrictions: []
      },
      {
        type: AdType.atv,
        position: 6,
        duration: 30,
        adId: '',
        slotRestrictions: []
      },
      {
        type: AdType.atv,
        position: 8,
        duration: 15,
        adId: '',
        slotRestrictions: []
      },
      {
        type: AdType.atv,
        position: 10,
        duration: 15,
        adId: '',
        slotRestrictions: []
      }
    ]
  }
};
