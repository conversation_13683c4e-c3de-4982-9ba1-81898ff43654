import { DaiAdsProviderOutput, RequestHeaders } from '../../../../../interfaces';
import scenario1 from '../../../../../assets/mocks/TEST/v1_0_0/scenario1.json';
import { outputData } from './outputFromFactory';
import { AdOceanProxyDaiAdsProvider } from '../../AdOcean/AdOceanProxyDaiAdsProvider';
import { response1, response2 } from './initialResponseATV';
import { Test, TestingModule } from '@nestjs/testing';
import { DaiAdsProviderFactory } from '../../daiAdsProviderFactory';
import { CustomParamsGenerator } from '../../../customParamsGenerator.service';
import { RequestMacroParams } from '../../../../configuration/injectReqParams/requestMacroParams';
import { BreakConnector, IConfiguration } from 'adpod-tools';
import { Protocol } from '../../../../../models/protocol.model';
import { IDeapProfilesService, DeapProfileService } from '../../../deapProfiles.service';
import axios from 'axios';
import { TcfService } from '../../../tcf.service';
import {
  FreeWheelFillersAdsProviderService,
  IFreeWheelFillersAdsProviderService
} from '../../FreeWheel/freeWheelFillersAdsProvider.service';
import { FillerAdsService, IFillerAdsService } from 'adpod-aws';
import { TestCacheModule } from '../../../../../libs/testing';
import { TestRedisModule } from 'adpod-aws/dist/testing';
import { DebugService, IDebugService } from '../../../../../libs';

describe('AdOceanProxy service test suite', () => {
  let service: AdOceanProxyDaiAdsProvider;
  let factory: DaiAdsProviderFactory;

  beforeAll(async () => {
    const app: TestingModule = await Test.createTestingModule({
      imports: [TestCacheModule, TestRedisModule],
      providers: [
        CustomParamsGenerator,
        AdOceanProxyDaiAdsProvider,
        TcfService,
        {
          provide: IDeapProfilesService,
          useClass: DeapProfileService
        },
        {
          provide: IDebugService,
          useClass: DebugService
        }
      ]
    }).compile();

    service = app.get(AdOceanProxyDaiAdsProvider);

    const factoryApp: TestingModule = await Test.createTestingModule({
      imports: [TestCacheModule, TestRedisModule],
      providers: [
        DaiAdsProviderFactory,
        CustomParamsGenerator,
        TcfService,
        {
          provide: IFreeWheelFillersAdsProviderService,
          useClass: FreeWheelFillersAdsProviderService
        },
        {
          provide: IFillerAdsService,
          useClass: FillerAdsService
        },
        {
          provide: IDeapProfilesService,
          useClass: DeapProfileService
        }
      ]
    }).compile();

    factory = factoryApp.get(DaiAdsProviderFactory);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.resetAllMocks();
  });

  it('should provide Ad Coordinator - after refactor', async () => {
    const requestSpy = jest.spyOn(axios, 'get');
    requestSpy.mockResolvedValueOnce(response1);

    const adCoordinator = await service.provideAdCoordinator(
      outputData as DaiAdsProviderOutput
    );

    expect(requestSpy).toHaveBeenCalledTimes(1);
    expect(adCoordinator).toMatchSnapshot();
  });

  it('should return Adserver response - before refactor', async () => {
    const requestSpy = jest.spyOn(axios, 'get');
    requestSpy.mockResolvedValue(response2);

    const requestMacroParams = new RequestMacroParams('12345', Protocol.https, '1');
    const inputArgs = await factory.init({
      configuration: scenario1 as unknown as IConfiguration,
      headers: [] as RequestHeaders,
      connector: BreakConnector.adoceanSlotSchedule,
      requestMacroParams
    });

    const result = await service.provideAdCoordinator(inputArgs);

    expect(requestSpy).toHaveBeenCalledTimes(1);
    expect(result).toMatchSnapshot();
  });
});
