import { AdType, BreakConnector } from 'adpod-tools';
import { DaiAdsProviderOutput } from '../../../../../interfaces';

export const outputData: DaiAdsProviderOutput = {
  request: {
    headers: {
      'x-device-ip': '*************',
      'x-device-user-agent':
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
      'user-agent': 'PostmanRuntime/7.39.0'
    },
    gdpr: undefined,
    gdprConsent: undefined,
    custParams: 'TEST_AUTO=true&v=ADO_PROXY_TEST',
    uid: undefined
  },
  config: {
    breakId: '7516634459727321',
    blockDuration: 175,
    connector: BreakConnector.adoceanSlotSchedule,
    version: 'ADO_PROXY_TEST',
    channel: 'TVN',
    atvSpots: [
      {
        type: AdType.atv,
        position: 1,
        duration: 30,
        adId: 'r313297.mov',
        adrequest:
          'http://tvn.adocean.pl/ad.xml?aocodetype=1/id=A1w3VxpUJ7UcI7sa6HfkBq7OAyJJ31IAAF6sV.rSP2L.S7/ch=TVN/bid=7516634459727321/p=1/mid1dur=30/mid1maxdur=30/mid1mindur=30/ct=linear/gdpr=/gdpr_consent=/adid=r313297.mov/tvn_restriction_labels=exclude_type_BEER'
      }
    ]
  }
};
