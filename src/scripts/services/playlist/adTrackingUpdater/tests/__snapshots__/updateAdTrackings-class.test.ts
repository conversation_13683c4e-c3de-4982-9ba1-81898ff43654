import { AdImpression, Channel, IAd, deepCopy, AdVast4Normalized } from 'adpod-tools';
import { Protocol } from '../../../../../../models/protocol.model';
import adoAdData from '../../../../../../assets/mocks/adoAdData.json';
import adoResponse from '../../../../../../assets/mocks/adoResponse.json';
import adoResponseMultipleCreatives from '../../../../../../assets/mocks/adoResponseMultipleCreatives.json';
import adoUpdatedResponseMultipleCreatives from '../../../../../../assets/mocks/adoUpdatedResponseMultipleCreatives.json';
import gamAdData from '../../../../../../assets/mocks/gamAdData.json';
import gamResponse from '../../../../../../assets/mocks/gamResponse.json';
import { AdTrackingsUpdater } from '../../updateAdTrackings-class';
import { Version } from '../../../../../../models/version.model';

describe('updateAdTrackings test suite', () => {
  process.env.npm_package_version = '1.0.0';

  describe('addAdditionalAdTrackings test suite', () => {
    it('should update Tracking and Impression tags single creative', () => {
      const updatedAd = new AdTrackingsUpdater(
        adoResponse as AdVast4Normalized,
        1,
        adoAdData as IAd,
        Channel.ttv,
        Version.v116,
        Protocol.http,
        '7777'
      ).getUpdatedAd();

      expect(updatedAd).toMatchSnapshot();
    });

    it('should update Tracking and Impression tags multiple creatives', () => {
      const updatedAd = new AdTrackingsUpdater(
        adoResponseMultipleCreatives as AdVast4Normalized,
        1,
        adoAdData as IAd,
        Channel.ttv,
        Version.v116,
        Protocol.http,
        '7777'
      ).getUpdatedAd();

      expect(updatedAd).toMatchSnapshot();
    });

    it('should update Tracking and Impression tags connector=gam', () => {
      const updatedAd = new AdTrackingsUpdater(
        gamResponse as AdVast4Normalized,
        1,
        gamAdData as IAd,
        Channel.ttv,
        Version.v116,
        Protocol.http,
        '7777'
      ).getUpdatedAd();

      expect(updatedAd).toMatchSnapshot();
    });
  });

  describe('getAdditionalAdTrackings test suite', () => {
    test('add additional trackings', () => {
      const instance = new AdTrackingsUpdater(
        adoResponse as any,
        1,
        adoAdData as IAd,
        Channel.ttv,
        Version.v116,
        Protocol.http
      );

      const validTracking = {
        _attributes: { event: 'midpoint' },
        _cdata:
          'http://dai-discoveryengage.tvn.pl/?ed=p%3D8%2Fbid%3D7515506236310321%2Fch%3DTTV%2Fuadid%3Dr222462%2Fv%3Dv1_1_4%2Fuid%3D777%2Fe%3D50%2Fmode%3Ddai%2Fdai_ad%3DIKEA_30%2Fdur%3D15%2Fbt%3Ddai%2Fapp_version%3D1.0.0'
      };

      const additionalAdTrackings = instance.getAdditionalAdTrackings([validTracking]);

      expect(additionalAdTrackings).toEqual(
        expect.arrayContaining([expect.objectContaining(validTracking)])
      );
    });
  });

  describe('getAdditionalImpressions test suite', () => {
    test('get additional impressions', () => {
      const validImpression = [
        {
          _attributes: { id: '' },
          _cdata:
            'http://dai-discoveryengage.tvn.pl/?ed=p%3D8%2Fbid%3D7515506236310321%2Fch%3DTTV%2Fuadid%3Dr222462%2Fv%3Dv1_1_6%2Fe%3Dimpression%2Fmode%3Ddai%2Fdai_ad%3DJhtEtLJvTG0%2Fdur%3D15%2Fbt%3Ddai%2Fm%3DPL%2Ft%3Ddai%2Fapp_version%3D1.0.0'
        }
      ];

      const instance = new AdTrackingsUpdater(
        adoResponse as AdVast4Normalized,
        1,
        adoAdData as IAd,
        Channel.ttv,
        Version.v116,
        Protocol.http
      );

      const mock = jest.spyOn(instance, 'campaignAttr', 'get').mockReturnValue(undefined);

      const additionalImpressions = instance.getAdditionalAdImpressions();

      expect(additionalImpressions).toEqual(validImpression);
      mock.mockRestore();
    });

    test('get additional impressions with array of creatives', () => {
      const responseCopy = deepCopy(adoUpdatedResponseMultipleCreatives);
      responseCopy.InLine.Creatives.Creative = [
        { UniversalAdId: { _text: 'test1' } },
        { UniversalAdId: { _text: 'test2' } },
        { UniversalAdId: { _text: 'test3' } }
      ] as any;

      const instance = new AdTrackingsUpdater(
        responseCopy as AdVast4Normalized,
        1,
        adoAdData as IAd,
        Channel.ttv,
        Version.v114,
        Protocol.http,
        '777'
      );

      const validImpression = [
        {
          _attributes: { id: '' },
          _cdata:
            'http://dai-discoveryengage.tvn.pl/?ed=p%3D8%2Fbid%3D7515506236310321%2Fch%3DTTV%2Fuadid%3Dr222462%2Fv%3Dv1_1_4%2Fuid%3D777%2Fe%3Dimpression%2Fmode%3Ddai%2Fdur%3D15%2Fbt%3Ddai%2Fm%3DPL%2Ft%3Ddai%2Fapp_version%3D1.0.0'
        }
      ];

      const mock = jest.spyOn(instance, 'campaignAttr', 'get').mockReturnValue(undefined);

      const additionalImpressions = instance.getAdditionalAdImpressions();

      expect(additionalImpressions).toEqual(validImpression);
      mock.mockRestore();
    });

    test('get additional impressions with array of creatives without text inside UniversalAdIds', () => {
      const responseCopy = deepCopy(adoUpdatedResponseMultipleCreatives);
      responseCopy.InLine.Creatives.Creative = [
        {
          Linear: { Duration: { _text: '00:00:05' }, TrackingEvents: { Tracking: undefined } },
          UniversalAdId: { _text: undefined }
        },
        {
          Linear: { Duration: { _text: '00:00:10' }, TrackingEvents: { Tracking: undefined } },
          UniversalAdId: { _text: undefined }
        },
        {
          Linear: { Duration: { _text: '00:00:15' }, TrackingEvents: { Tracking: undefined } },
          UniversalAdId: { _text: undefined }
        }
      ] as any;

      const instance = new AdTrackingsUpdater(
        responseCopy as AdVast4Normalized,
        1,
        adoAdData as IAd,
        Channel.ttv,
        Version.v114,
        Protocol.http,
        '777'
      );

      const validImpression = [
        {
          _attributes: { id: '' },
          _cdata:
            'http://dai-discoveryengage.tvn.pl/?ed=p%3D8%2Fbid%3D7515506236310321%2Fch%3DTTV%2Fuadid%3Dr222462%2Fv%3Dv1_1_4%2Fuid%3D777%2Fe%3Dimpression%2Fmode%3Ddai%2Fdai_ad%3Dnot%20provided%2Cnot%20provided%2Cnot%20provided%2Fdur%3D15%2Fbt%3Ddai%2Fm%3DPL%2Ft%3Ddai%2Fapp_version%3D1.0.0'
        }
      ];

      const mock = jest.spyOn(instance, 'campaignAttr', 'get').mockReturnValue(undefined);

      const additionalImpressions = instance.getAdditionalAdImpressions();

      expect(additionalImpressions).toEqual(validImpression);
      mock.mockRestore();
    });

    test('get additional impressions with array of creatives with mixed input inside UniversalAdIds', () => {
      const responseCopy = deepCopy(adoUpdatedResponseMultipleCreatives);
      responseCopy.InLine.Creatives.Creative = [
        {
          Linear: { Duration: { _text: '00:00:05' }, TrackingEvents: { Tracking: undefined } },
          UniversalAdId: { _text: undefined }
        },
        {
          Linear: { Duration: { _text: '00:00:10' }, TrackingEvents: { Tracking: undefined } },
          UniversalAdId: { _text: 'test123' }
        },
        {
          Linear: { Duration: { _text: '00:00:15' }, TrackingEvents: { Tracking: undefined } },
          UniversalAdId: { _text: undefined }
        }
      ] as any;

      const instance = new AdTrackingsUpdater(
        responseCopy as AdVast4Normalized,
        1,
        adoAdData as IAd,
        Channel.ttv,
        Version.v114,
        Protocol.http,
        '777'
      );

      const validImpression = [
        {
          _attributes: { id: '' },
          _cdata:
            'http://dai-discoveryengage.tvn.pl/?ed=p%3D8%2Fbid%3D7515506236310321%2Fch%3DTTV%2Fuadid%3Dr222462%2Fv%3Dv1_1_4%2Fuid%3D777%2Fe%3Dimpression%2Fmode%3Ddai%2Fdai_ad%3Dnot%20provided%2Ctest123%2Cnot%20provided%2Fdur%3D15%2Fbt%3Ddai%2Fm%3DPL%2Ft%3Ddai%2Fapp_version%3D1.0.0'
        }
      ];

      const mock = jest.spyOn(instance, 'campaignAttr', 'get').mockReturnValue(undefined);

      const additionalImpressions = instance.getAdditionalAdImpressions();

      expect(additionalImpressions).toEqual(validImpression);
      mock.mockRestore();
    });
  });

  describe('addAdditionalImpression test suite', () => {
    test('add additional impression', () => {
      const additionalImpression: AdImpression[] = [
        {
          _attributes: {
            id: 'TEST'
          },
          _cdata:
            'http://dai-discoveryengage.tvn.pl/?ed=p%3D3%2Fbid%3D20220310MTIT000022357%2Fch%3DMTIT%2Fuadid%3Dr123456%2Fv%3Dnowtilus_v1_0_0%2Fuid%3D30948679-2b37-4729-bfe1-249f0b2f54eb%2Fe%3Dimpression%2Fmode%3Ddai%2Fapp_version%3D1.0.0'
        }
      ];

      const instance = new AdTrackingsUpdater(
        adoResponse as AdVast4Normalized,
        1,
        adoAdData as IAd,
        Channel.ttv,
        Version.v116,
        Protocol.http
      );

      jest.spyOn(instance, 'getAdditionalAdImpressions').mockReturnValue(additionalImpression);

      const adData = instance.addAdditionalImpression();
      const impressions = adData.InLine?.Impression as AdImpression[];

      expect(impressions.at(-1)).toMatchObject(additionalImpression[0]);
    });
  });

  describe('haveAdTrackings test suite', () => {
    test('there should be tracking codes', () => {
      const instance = new AdTrackingsUpdater(
        adoResponse as AdVast4Normalized,
        1,
        adoAdData as IAd,
        Channel.ttv,
        Version.v116,
        Protocol.http
      );
      const trackingEvents = instance.haveAdTrackings();

      expect(trackingEvents).toEqual(true);
    });
  });
});
