import { Injectable } from '@nestjs/common';
import {
  AdCreativeNormalized,
  AdVast4Normalized,
  getMediaFileType,
  MediaFile
} from 'adpod-tools';

export abstract class IAdSlotPlaylistTransformer {
  abstract updateAdServerAd(
    adServerAd: AdVast4Normalized,
    position: number,
    isVast4: boolean,
    configId: string,
    timeOffset: string,
    isAdOceanConnector: boolean,
    isGamConnector: boolean,
    diffCreatives: boolean
  ): AdVast4Normalized;
}

@Injectable()
export class AdSlotPlaylistTransformer implements IAdSlotPlaylistTransformer {
  private updateAdOceanAdAttr(adServerAd: AdVast4Normalized, position: number) {
    adServerAd._attributes.id = `Midroll_${position}`;
    return adServerAd;
  }

  private prefixAdOceanIdValue(value?: string): string | undefined {
    return value ? `dai_${value}` : undefined;
  }

  private updateAdOceanMediaFiles(creative: AdCreativeNormalized): MediaFile[] {
    const mediaFiles = creative.Linear?.MediaFiles?.MediaFile ?? [];
    const hasMediaFiles = mediaFiles.length > 0;
    const creativeId = creative._attributes?.id;
    const adId =
      hasMediaFiles && creativeId ? this.prefixAdOceanIdValue(creativeId) : creativeId;

    if (!hasMediaFiles || !adId) return mediaFiles;

    const additionalMediaFile: MediaFile = {
      _attributes: {
        width: 1920,
        height: 1080,
        codec: 'H.264',
        delivery: 'streaming',
        type: 'application/dash+xml',
        minBitrate: 500000,
        maxBitrate: 6350000
      },
      _cdata: `https://r.dcs.redcdn.pl/dash/o2/rekwbd/SSAI/${adId!.replace('.mov', '')}/dash.smil`
    };

    return [...mediaFiles, additionalMediaFile];
  }

  private updateAdOceanAd(
    adServerAd: AdVast4Normalized,
    diffCreatives: boolean
  ): AdVast4Normalized {
    const creatives = adServerAd.InLine?.Creatives?.Creative;

    if (!creatives?.length) return adServerAd;

    // eslint-disable-next-line no-unreachable-loop
    for (const [index, creative] of creatives.entries()) {
      // update MediaFiles
      adServerAd.InLine!.Creatives.Creative[index].Linear!.MediaFiles.MediaFile =
        this.updateAdOceanMediaFiles(creative);

      if (diffCreatives) {
        // for diffCreatives add dai_ prefix for Creative attrs (id, AdID)
        const attributes = creative._attributes;
        if (attributes) {
          Object.assign(adServerAd.InLine!.Creatives.Creative[index]._attributes!, {
            id: this.prefixAdOceanIdValue(attributes.id),
            AdID: this.prefixAdOceanIdValue(attributes.AdID)
          });
        }

        // for diffCreatives add dai_ prefix for UniversalAdId _text and attr (idValue)
        const universalAdId = creative.UniversalAdId;
        if (universalAdId) {
          Object.assign(
            adServerAd.InLine!.Creatives.Creative[index].UniversalAdId!._attributes!,
            { idValue: this.prefixAdOceanIdValue(universalAdId._attributes.idRegistry) }
          );

          adServerAd.InLine!.Creatives.Creative[index].UniversalAdId!._text =
            this.prefixAdOceanIdValue(universalAdId._text) ?? '';
        }
      }

      return adServerAd;
    }

    return adServerAd;
  }

  private updateAdMezzanine(adServerAd: AdVast4Normalized): AdVast4Normalized {
    const creativeTagArray = adServerAd.InLine?.Creatives.Creative;

    if (!creativeTagArray) {
      return adServerAd;
    }

    for (const [creativeTagIndex, creativeTagEl] of creativeTagArray.entries()) {
      const currentMediaFiles = creativeTagEl.Linear?.MediaFiles;
      const currentMezzanine = currentMediaFiles?.Mezzanine;
      const currentMezzanineCdata = currentMezzanine?._cdata;

      if (currentMezzanine && currentMezzanineCdata && currentMediaFiles) {
        adServerAd.InLine!.Creatives.Creative[creativeTagIndex].Linear!.MediaFiles = {
          ...currentMediaFiles,
          Mezzanine: {
            ...currentMezzanine,
            _attributes: {
              delivery: 'progressive',
              type: getMediaFileType(currentMezzanine._cdata!),
              width: 1920,
              height: 1080
            }
          }
        };
      }
    }

    return adServerAd;
  }

  private updateBaseAdAttributes(
    adServerAd: AdVast4Normalized,
    position: number,
    configId: string,
    isVast4: boolean,
    timeOffset: string
  ): AdVast4Normalized {
    Object.assign(adServerAd._attributes, {
      sequence: position,
      breakId: configId,
      conditionalAd: false,
      timeOffset: isVast4 ? timeOffset : undefined
    });

    return adServerAd;
  }

  private updateAdSystemVersionAttr(adServerAd: AdVast4Normalized): AdVast4Normalized {
    if (adServerAd.InLine?.AdSystem) {
      Object.assign(adServerAd.InLine!.AdSystem, {
        _attributes: { version: '4.0' }
      });
    }

    return adServerAd;
  }

  public updateAdServerAd(
    adServerAd: AdVast4Normalized,
    position: number,
    isVast4: boolean,
    configId: string,
    timeOffset: string,
    isAdOceanConnector: boolean,
    isGamConnector: boolean,
    diffCreatives: boolean
  ): AdVast4Normalized {
    adServerAd = this.updateBaseAdAttributes(
      adServerAd,
      position,
      configId,
      isVast4,
      timeOffset
    );

    adServerAd = this.updateAdSystemVersionAttr(adServerAd);

    if (isAdOceanConnector) {
      adServerAd = this.updateAdOceanAdAttr(adServerAd, position);
      adServerAd = this.updateAdOceanAd(adServerAd, diffCreatives);
    }

    if (isAdOceanConnector || isGamConnector) {
      adServerAd = this.updateAdMezzanine(adServerAd);
    }

    return adServerAd;
  }
}
