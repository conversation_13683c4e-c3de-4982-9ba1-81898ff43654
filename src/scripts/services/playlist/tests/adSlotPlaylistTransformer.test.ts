import { Test, TestingModule } from '@nestjs/testing';
import {
  AdSlotPlaylistTransformer,
  IAdSlotPlaylistTransformer
} from '../adSlotPlaylistTransformer.service';
import adOcean30sResponse from './mocks/adOcean30sResponse.json';
import { AdVast4Normalized } from 'adpod-tools';

describe('AdSlotPlaylistTransformer service test suite', () => {
  let service: IAdSlotPlaylistTransformer;

  beforeAll(async () => {
    const app: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: IAdSlotPlaylistTransformer,
          useClass: AdSlotPlaylistTransformer
        }
      ]
    }).compile();

    service = app.get(IAdSlotPlaylistTransformer);
  });

  afterEach(() => {
    jest.resetAllMocks();
    jest.clearAllMocks();
  });

  const position = 11;
  const breakId = '7516965158016321';
  const timeOffset = '2025-06-03T00:48:34+02:00';

  // Ad id attr
  it('should correctly update AdOcean ad id attr', async () => {
    const adOceanAdWithDaiCreativesVmap = service.updateAdServerAd(
      structuredClone(adOcean30sResponse) as unknown as AdVast4Normalized,
      position,
      false,
      breakId,
      timeOffset,
      true,
      false,
      true
    );

    expect(adOceanAdWithDaiCreativesVmap._attributes.id).toContain('Midroll_');
  });

  it('should not update for non AdOcean Ads', async () => {
    const adOceanAdWithDaiCreativesVmap = service.updateAdServerAd(
      structuredClone(adOcean30sResponse) as unknown as AdVast4Normalized,
      position,
      false,
      breakId,
      timeOffset,
      false,
      false,
      true
    );

    expect(adOceanAdWithDaiCreativesVmap._attributes.id).not.toContain('Midroll_');
  });

  // UniversalAdId
  it('should correctly update UniversalAdId idValue attr', async () => {
    const adOceanAdWithDaiCreativesVmap = service.updateAdServerAd(
      structuredClone(adOcean30sResponse) as unknown as AdVast4Normalized,
      position,
      false,
      breakId,
      timeOffset,
      true,
      false,
      true
    );

    expect(
      adOceanAdWithDaiCreativesVmap.InLine!.Creatives.Creative[0].UniversalAdId!._attributes
        .idValue
    ).toContain('dai_');
  });

  it('should correctly update UniversalAdId tag value', async () => {
    const adOceanAdWithDaiCreativesVmap = service.updateAdServerAd(
      structuredClone(adOcean30sResponse) as unknown as AdVast4Normalized,
      position,
      false,
      breakId,
      timeOffset,
      true,
      false,
      true
    );

    expect(
      adOceanAdWithDaiCreativesVmap.InLine!.Creatives.Creative[0].UniversalAdId!._text
    ).toContain('dai_');
  });

  it('should not update UniversalAdId idValue attr if diffCreatives is disabled', async () => {
    const adOceanAdWithDaiCreatives = service.updateAdServerAd(
      structuredClone(adOcean30sResponse) as unknown as AdVast4Normalized,
      position,
      false,
      breakId,
      timeOffset,
      true,
      false,
      false
    );

    expect(
      adOceanAdWithDaiCreatives.InLine!.Creatives.Creative[0].UniversalAdId!._attributes
        .idValue
    ).not.toContain('dai_');
  });

  it('should not update UniversalAdId tag value if diffCreatives is disabled', async () => {
    const adOceanAdWithDaiCreatives = service.updateAdServerAd(
      structuredClone(adOcean30sResponse) as unknown as AdVast4Normalized,
      position,
      false,
      breakId,
      timeOffset,
      true,
      false,
      false
    );

    expect(
      adOceanAdWithDaiCreatives.InLine!.Creatives.Creative[0].UniversalAdId!._text
    ).not.toContain('dai_');
  });

  it('should not update UniversalAdId idValue attr if diffCreatives is enabled & it is non AdOcean ad', async () => {
    const adOceanAdWithDaiCreatives = service.updateAdServerAd(
      structuredClone(adOcean30sResponse) as unknown as AdVast4Normalized,
      position,
      false,
      breakId,
      timeOffset,
      false,
      false,
      true
    );

    expect(
      adOceanAdWithDaiCreatives.InLine!.Creatives.Creative[0].UniversalAdId!._attributes
        .idValue
    ).not.toContain('dai_');
  });

  it('should not update UniversalAdId tag value if diffCreatives is enabled & it is non AdOcean ad', async () => {
    const adOceanAdWithDaiCreatives = service.updateAdServerAd(
      structuredClone(adOcean30sResponse) as unknown as AdVast4Normalized,
      position,
      false,
      breakId,
      timeOffset,
      false,
      false,
      true
    );

    expect(
      adOceanAdWithDaiCreatives.InLine!.Creatives.Creative[0].UniversalAdId!._text
    ).not.toContain('dai_');
  });

  it('should keep oryginal UniversalAdId idRegistry attr value', async () => {
    const adOceanAdWithDaiCreatives = service.updateAdServerAd(
      structuredClone(adOcean30sResponse) as unknown as AdVast4Normalized,
      11,
      false,
      '7516965158016321',
      '2025-06-03T00:48:34+02:00',
      true,
      false,
      true
    );

    expect(
      adOceanAdWithDaiCreatives.InLine!.Creatives.Creative[0].UniversalAdId!._attributes
        .idRegistry
    ).toEqual('TVN_Registry');
  });

  // base attrs
  it('should update base attributes and AdSystem version for vast4', () => {
    const adOceanAdWithDaiCreatives = service.updateAdServerAd(
      structuredClone(adOcean30sResponse) as unknown as AdVast4Normalized,
      position,
      true,
      breakId,
      timeOffset,
      true,
      false,
      true
    );

    expect(adOceanAdWithDaiCreatives._attributes.sequence).toBe(position);
    expect(adOceanAdWithDaiCreatives._attributes.breakId).toBe(breakId);
    expect(adOceanAdWithDaiCreatives._attributes.timeOffset).toBe(timeOffset);
    expect(adOceanAdWithDaiCreatives.InLine?.AdSystem?._attributes?.version).toBe('4.0');
  });

  it('should update base attributes without timeOffset and AdSystem version for vmap', () => {
    const adOceanAdWithDaiCreatives = service.updateAdServerAd(
      structuredClone(adOcean30sResponse) as unknown as AdVast4Normalized,
      position,
      false,
      breakId,
      timeOffset,
      true,
      false,
      true
    );

    expect(adOceanAdWithDaiCreatives._attributes.sequence).toBe(position);
    expect(adOceanAdWithDaiCreatives._attributes.breakId).toBe(breakId);
    expect(adOceanAdWithDaiCreatives._attributes.timeOffset).not.toBe(timeOffset);
    expect(adOceanAdWithDaiCreatives.InLine?.AdSystem?._attributes?.version).toBe('4.0');
  });

  // Creative tag - id
  it('should correctly update Creative id and AdID attrs', async () => {
    const adOceanAdWithDaiCreativesVmap = service.updateAdServerAd(
      structuredClone(adOcean30sResponse) as unknown as AdVast4Normalized,
      position,
      false,
      breakId,
      timeOffset,
      true,
      false,
      true
    );

    expect(
      adOceanAdWithDaiCreativesVmap.InLine!.Creatives.Creative[0]._attributes!.id
    ).toContain('dai_');

    expect(
      adOceanAdWithDaiCreativesVmap.InLine!.Creatives.Creative[0]._attributes!.AdID
    ).toContain('dai_');
  });

  it('should not update Creative id and AdID attrs if diffCreatives disabled', async () => {
    const adOceanAdWithDaiCreativesVmap = service.updateAdServerAd(
      structuredClone(adOcean30sResponse) as unknown as AdVast4Normalized,
      position,
      false,
      breakId,
      timeOffset,
      true,
      false,
      false
    );

    expect(
      adOceanAdWithDaiCreativesVmap.InLine!.Creatives.Creative[0]._attributes!.id
    ).not.toContain('dai_');

    expect(
      adOceanAdWithDaiCreativesVmap.InLine!.Creatives.Creative[0]._attributes!.AdID
    ).not.toContain('dai_');
  });

  it('should not update Creative id and AdID attrs for non AdOcean ad', async () => {
    const adOceanAdWithDaiCreativesVmap = service.updateAdServerAd(
      structuredClone(adOcean30sResponse) as unknown as AdVast4Normalized,
      position,
      false,
      breakId,
      timeOffset,
      false,
      false,
      true
    );

    expect(
      adOceanAdWithDaiCreativesVmap.InLine!.Creatives.Creative[0]._attributes!.id
    ).not.toContain('dai_');

    expect(
      adOceanAdWithDaiCreativesVmap.InLine!.Creatives.Creative[0]._attributes!.id
    ).not.toContain('dai_');
  });

  // Media files
  it('should update Media Files for AdOcean', () => {
    const adOceanAdWithDaiCreativesVmap = service.updateAdServerAd(
      structuredClone(adOcean30sResponse) as unknown as AdVast4Normalized,
      position,
      false,
      breakId,
      timeOffset,
      true,
      false,
      true
    );

    const creative = adOceanAdWithDaiCreativesVmap.InLine?.Creatives?.Creative[0];
    const mediaFiles = creative?.Linear?.MediaFiles?.MediaFile;

    const additionalMediaFile = mediaFiles?.find((file) =>
      file._cdata?.includes('/dash/o2/rekwbd/SSAI/dai_I320066/dash.smil')
    );

    expect(additionalMediaFile).toBeDefined();
  });

  it('should not update Media Files for non AdOcean ads', () => {
    const adOceanAdWithDaiCreativesVmap = service.updateAdServerAd(
      structuredClone(adOcean30sResponse) as unknown as AdVast4Normalized,
      position,
      false,
      breakId,
      timeOffset,
      false,
      false,
      true
    );

    const creative = adOceanAdWithDaiCreativesVmap.InLine?.Creatives?.Creative[0];
    const mediaFiles = creative?.Linear?.MediaFiles?.MediaFile;

    const additionalMediaFile = mediaFiles?.find((file) =>
      file._cdata?.includes('/dash/o2/rekwbd/SSAI/dai_I320066/dash.smil')
    );

    expect(additionalMediaFile).not.toBeDefined();
  });
});
