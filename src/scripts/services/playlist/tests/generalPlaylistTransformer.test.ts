import { Test, TestingModule } from '@nestjs/testing';
import { GeneralPlaylistTransformer } from '../generalPlaylistTransformer.service';
import { ICacheProvider, IDebugService } from '../../../../libs';
import { createMock } from '@golevelup/ts-jest';
import { CacheProviderMock } from '../../../../libs/testing';
import { Vmap, VmapNormalized } from '../../../../interfaces';
import scenario1 from '../../../../assets/mocks/TEST/v1_0_0/scenario1.json';
import {
  AdVast4Normalized,
  IConfiguration,
  prepareJsonVast4,
  Vast4Normalized
} from 'adpod-tools';

describe('GeneralPlaylistTransformer service test suite', () => {
  let service: GeneralPlaylistTransformer;
  let debugServiceMock: jest.Mocked<IDebugService>;
  let cacheProviderMock: CacheProviderMock;

  beforeEach(async () => {
    jest.resetAllMocks();
    jest.clearAllMocks();

    debugServiceMock = createMock();
    cacheProviderMock = createMock();

    const app: TestingModule = await Test.createTestingModule({
      providers: [
        GeneralPlaylistTransformer,
        {
          provide: IDebugService,
          useValue: debugServiceMock
        },
        {
          provide: ICacheProvider,
          useValue: cacheProviderMock
        }
      ]
    }).compile();

    service = app.get(GeneralPlaylistTransformer);
  });

  describe('injectPrefetchTimeToVmap test suite', () => {
    const vmapVast4: Vmap = {
      'vmap:VMAP': {
        _attributes: {
          'xmlns:vmap': 'http://iab.net/videosuite/vmap',
          version: '1.0'
        },
        'vmap:AdBreak': []
      }
    };

    const vmapVast4Prefetch: Vmap = {
      ...vmapVast4,
      'vmap:VMAP': {
        ...vmapVast4['vmap:VMAP'],
        _attributes: {
          ...vmapVast4['vmap:VMAP']._attributes,
          prefetch: '12345'
        }
      }
    };

    test('should inject prefetchTime', () => {
      expect(service.injectPrefetchTimeToVmap(vmapVast4, '12345')).toEqual(vmapVast4Prefetch);
    });

    test('should return same vmap', () => {
      expect(service.injectPrefetchTimeToVmap(vmapVast4, undefined)).toEqual(vmapVast4);
    });
  });

  describe('convertToVmap test suite', () => {
    const emptyVast4: Vast4Normalized = prepareJsonVast4([]);

    const emptyVmapVast4: VmapNormalized = {
      'vmap:VMAP': {
        _attributes: {
          'xmlns:vmap': 'http://iab.net/videosuite/vmap',
          version: '1.0'
        },
        'vmap:AdBreak': [
          {
            _attributes: {
              timeOffset: '2020-09-24T22:30:05+02:00',
              breakType: 'mirrored',
              breakId: '123'
            },
            'vmap:AdSource': {
              _attributes: {
                id: 'ads',
                allowMultipleAds: 'true',
                followRedirects: 'true'
              },
              'vmap:VASTAdData': {
                VAST: {
                  _attributes: {
                    version: '4.0',
                    'xmlns:xs': 'http://www.w3.org/2001/XMLSchema',
                    xmlns: 'http://www.iab.com/VAST'
                  },
                  Ad: []
                }
              }
            }
          }
        ]
      }
    };

    const oneAdVast4: Vast4Normalized = {
      VAST: {
        _attributes: {
          version: '4.0',
          'xmlns:xs': 'http://www.w3.org/2001/XMLSchema',
          xmlns: 'http://www.iab.com/VAST'
        },
        Ad: [
          {
            _attributes: {
              id: 'Preroll',
              breakId: '123',
              campaignId: 'CA_37178,OR_1,CR_1',
              sequence: 1,
              conditionalAd: false
            },
            InLine: {
              AdSystem: {
                _text: 'TVN',
                _attributes: {
                  version: '1_0_0'
                }
              },
              AdTitle: {
                _text: 'TVN Video Ad'
              },
              Creatives: {
                Creative: [
                  {
                    Linear: {
                      Duration: {
                        _text: '00:00:15'
                      },
                      MediaFiles: {
                        MediaFile: [
                          {
                            _attributes: {
                              width: 720,
                              type: 'video/mp4',
                              height: 404,
                              delivery: 'progressive'
                            },
                            _cdata: 'link'
                          }
                        ]
                      },
                      VideoClicks: {
                        ClickThrough: {
                          _attributes: {
                            id: ''
                          },
                          _cdata: 'link'
                        }
                      },
                      TrackingEvents: {
                        Tracking: [
                          {
                            _attributes: {
                              event: 'start'
                            },
                            _cdata: 'link'
                          },
                          {
                            _attributes: {
                              event: 'firstQuartile'
                            },
                            _cdata: 'link'
                          },
                          {
                            _attributes: {
                              event: 'midpoint'
                            },
                            _cdata: 'link'
                          },
                          {
                            _attributes: {
                              event: 'thirdQuartile'
                            },
                            _cdata: 'link'
                          },
                          {
                            _attributes: {
                              event: 'complete'
                            },
                            _cdata: 'link'
                          }
                        ]
                      }
                    }
                  },
                  {
                    CompanionAds: undefined
                  }
                ]
              },
              Impression: [
                {
                  _attributes: {
                    id: ''
                  },
                  _cdata: 'link'
                },
                {
                  _attributes: {
                    id: ''
                  }
                },
                {
                  _attributes: {
                    id: ''
                  }
                }
              ],
              Error: [],
              Extensions: {
                Extension: []
              }
            }
          }
        ]
      }
    };

    const vmapVast4: VmapNormalized = {
      'vmap:VMAP': {
        _attributes: {
          'xmlns:vmap': 'http://iab.net/videosuite/vmap',
          version: '1.0'
        },
        'vmap:AdBreak': [
          {
            _attributes: {
              timeOffset: '2020-09-24T22:30:05+02:00',
              breakType: 'dai',
              breakId: '123'
            },
            'vmap:AdSource': {
              _attributes: {
                id: 'ads',
                allowMultipleAds: 'true',
                followRedirects: 'true'
              },
              'vmap:VASTAdData': oneAdVast4
            }
          }
        ]
      }
    };

    test('vast4 has no references to vmap vast 2', async () => {
      expect(
        await service.convertToVmap([emptyVast4], [scenario1 as unknown as IConfiguration])
      ).not.toBe(emptyVmapVast4);
    });

    test('parsed empty vast4 is equal to proper empty vmap vast4', async () => {
      expect(
        await service.convertToVmap([emptyVast4], [scenario1 as unknown as IConfiguration])
      ).toEqual(emptyVmapVast4);
    });

    test('parsed vast4 with one ad to vmap vast4', async () => {
      expect(
        await service.convertToVmap([oneAdVast4], [scenario1 as unknown as IConfiguration])
      ).toEqual(vmapVast4);
    });
  });

  describe('convertToVAST4 test suite', () => {
    const jsonVast4: Vast4Normalized = {
      VAST: {
        _attributes: {
          version: '4.0',
          'xmlns:xs': 'http://www.w3.org/2001/XMLSchema',
          xmlns: 'http://www.iab.com/VAST'
        },
        Ad: []
      }
    };

    const adVasts4: AdVast4Normalized = {
      _attributes: {
        id: 'Preroll',
        breakId: '1',
        campaignId: 'CA_37178,OR_1,CR_1',
        sequence: 1,
        conditionalAd: false
      },
      InLine: {
        AdSystem: {
          _text: 'TVN',
          _attributes: { version: '1_0_0' }
        },
        AdTitle: { _text: 'TVN Video Ad' },
        Creatives: {
          Creative: [
            {
              Linear: {
                Duration: {
                  _text: '00:00:15'
                },
                MediaFiles: {
                  MediaFile: [
                    {
                      _attributes: {
                        width: 720,
                        type: 'video/mp4',
                        height: 404,
                        delivery: 'progressive'
                      },
                      _cdata: 'link'
                    }
                  ]
                },
                VideoClicks: {
                  ClickThrough: {
                    _attributes: { id: '' },
                    _cdata: 'link'
                  }
                },
                TrackingEvents: {
                  Tracking: [
                    {
                      _attributes: { event: 'start' },
                      _cdata: 'link'
                    },
                    {
                      _attributes: { event: 'firstQuartile' },
                      _cdata: 'link'
                    },
                    {
                      _attributes: { event: 'midpoint' },
                      _cdata: 'link'
                    },
                    {
                      _attributes: { event: 'thirdQuartile' },
                      _cdata: 'link'
                    },
                    {
                      _attributes: { event: 'complete' },
                      _cdata: 'link'
                    }
                  ]
                }
              }
            },
            { CompanionAds: undefined }
          ]
        },
        Impression: [
          { _attributes: { id: '' }, _cdata: 'link' },
          { _attributes: { id: '' } },
          { _attributes: { id: '' } }
        ],
        Error: [],
        Extensions: { Extension: [] }
      }
    };

    const vast4Array: Vast4Normalized = {
      ...jsonVast4,
      VAST: {
        ...jsonVast4.VAST,
        Ad: [adVasts4]
      }
    };

    const vast4ArrayMultiple: Vast4Normalized = {
      ...jsonVast4,
      VAST: {
        ...jsonVast4.VAST,
        Ad: [adVasts4, adVasts4]
      }
    };

    const vast4Prefetch: Vast4Normalized = {
      ...jsonVast4,
      VAST: {
        ...jsonVast4.VAST,
        _attributes: {
          ...jsonVast4.VAST._attributes,
          prefetch: '12345'
        }
      }
    };

    test('should stay same fo array Ad', () => {
      expect(service.convertToVAST4([vast4Array], undefined)).toEqual(vast4Array);
    });

    test('should combine multiple vasts into one with array of ads', () => {
      expect(service.convertToVAST4([vast4Array, vast4Array], undefined)).toEqual(
        vast4ArrayMultiple
      );
    });

    test('should return same Vast with no ads', () => {
      expect(service.convertToVAST4([jsonVast4], undefined)).toEqual(jsonVast4);
    });

    test('should return Vast with prefetch', () => {
      expect(service.convertToVAST4([jsonVast4], '12345')).toEqual(vast4Prefetch);
    });
  });
});
