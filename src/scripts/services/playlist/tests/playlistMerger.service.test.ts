import { Test, TestingModule } from '@nestjs/testing';
import { PlaylistMerger } from '../playlistMerger.service';
import { IConfiguration } from 'adpod-tools';
import { scenario1 } from '../../../../assets/mocks/simpleScenario1';
import { scenario2 } from '../../../../assets/mocks/simpleScenario2';
import { VastBuilder } from '../../tests/vastBuilder.helper';

describe('PlaylistMergerService', () => {
  let playlistMerger: PlaylistMerger;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      providers: [PlaylistMerger]
    }).compile();

    playlistMerger = app.get<PlaylistMerger>(PlaylistMerger);
  });

  describe('single merge', () => {
    test('should return first config for one version', async () => {
      const firstVast = new VastBuilder().addSlot(true).addSlot(true).addSlot(true).build();

      const result = playlistMerger.merge([
        {
          config: scenario1 as unknown as IConfiguration,
          breakDetails: {
            isWithReplacedAds: true,
            vast4Json: firstVast,
            adServerResponseLog: []
          }
        }
      ]);
      expect(result.breakDetails.vast4Json.VAST.Ad).toMatchObject([
        { _attributes: { id: 'Spot_1' } },
        { _attributes: { id: 'Spot_2' } },
        { _attributes: { id: 'Spot_3' } }
      ]);
    });

    test('should take all adslots from the first config', async () => {
      const firstVast = new VastBuilder().addSlot(true).addSlot(true).addSlot(true).build();
      const secondVast = new VastBuilder()
        .addSlot(false)
        .addSlot(true, 5)
        .addSlot(true, 6)
        .build();

      const result = playlistMerger.merge([
        {
          config: scenario1 as unknown as IConfiguration,
          breakDetails: {
            isWithReplacedAds: true,
            vast4Json: firstVast,
            adServerResponseLog: []
          }
        },
        {
          config: scenario2 as unknown as IConfiguration,
          breakDetails: {
            isWithReplacedAds: true,
            vast4Json: secondVast,
            adServerResponseLog: []
          }
        }
      ]);
      expect(result.breakDetails.vast4Json.VAST.Ad).toMatchObject([
        { _attributes: { id: 'Spot_1' } },
        { _attributes: { id: 'Spot_2' } },
        { _attributes: { id: 'Spot_3' } }
      ]);
    });

    test('should take all adslots from the second config', async () => {
      // arrange
      const firstVast = new VastBuilder().addSlot(false).addSlot(false).addSlot(false).build();
      const secondVast = new VastBuilder()
        .addSlot(true, 4)
        .addSlot(true, 5)
        .addSlot(true, 6)
        .build();

      // act
      const result = playlistMerger.merge([
        {
          config: scenario1 as unknown as IConfiguration,
          breakDetails: {
            isWithReplacedAds: true,
            vast4Json: firstVast,
            adServerResponseLog: []
          }
        },
        {
          config: scenario2 as unknown as IConfiguration,
          breakDetails: {
            isWithReplacedAds: true,
            vast4Json: secondVast,
            adServerResponseLog: []
          }
        }
      ]);

      // assert
      expect(result.breakDetails.vast4Json.VAST.Ad).toMatchObject([
        { _attributes: { id: 'Spot_4' } },
        { _attributes: { id: 'Spot_5' } },
        { _attributes: { id: 'Spot_6' } }
      ]);
    });

    test('should take first and third adslots from the first config and second from the second config', async () => {
      // arrange
      const firstVast = new VastBuilder().addSlot(true).addSlot(false).addSlot(true).build();
      const secondVast = new VastBuilder()
        .addSlot(false)
        .addSlot(true, 5)
        .addSlot(false)
        .build();

      // act
      const result = playlistMerger.merge([
        {
          config: scenario1 as unknown as IConfiguration,
          breakDetails: {
            isWithReplacedAds: true,
            vast4Json: firstVast,
            adServerResponseLog: []
          }
        },
        {
          config: scenario2 as unknown as IConfiguration,
          breakDetails: {
            isWithReplacedAds: true,
            vast4Json: secondVast,
            adServerResponseLog: []
          }
        }
      ]);

      // assert
      expect(result.breakDetails.vast4Json.VAST.Ad).toMatchObject([
        { _attributes: { id: 'Spot_1' } },
        { _attributes: { id: 'Spot_5' } },
        { _attributes: { id: 'Spot_3' } }
      ]);
    });

    test('should take second adslot from the first config and all other from the second config', async () => {
      // arrange
      const firstVast = new VastBuilder().addSlot(false).addSlot(true).addSlot(false).build();
      const secondVast = new VastBuilder()
        .addSlot(false, 4)
        .addSlot(true, 5)
        .addSlot(true, 6)
        .build();

      // act
      const result = playlistMerger.merge([
        {
          config: scenario1 as unknown as IConfiguration,
          breakDetails: {
            isWithReplacedAds: true,
            vast4Json: firstVast,
            adServerResponseLog: []
          }
        },
        {
          config: scenario2 as unknown as IConfiguration,
          breakDetails: {
            isWithReplacedAds: true,
            vast4Json: secondVast,
            adServerResponseLog: []
          }
        }
      ]);

      // assert
      expect(result.breakDetails.vast4Json.VAST.Ad).toMatchObject([
        { _attributes: { id: 'Spot_1' } },
        { _attributes: { id: 'Spot_2' } },
        { _attributes: { id: 'Spot_6' } }
      ]);
    });

    test('should take third adslot from the first config and all other from the second config', async () => {
      // arrange
      const firstVast = new VastBuilder()
        .addSlot(false)
        .addSlot(false)
        .addSlot(true)
        .addSlot(false)
        .build();
      const secondVast = new VastBuilder()
        .addSlot(true, 5)
        .addSlot(false, 6)
        .addSlot(true, 7)
        .addSlot(false, 8)
        .build();

      // act
      const result = playlistMerger.merge([
        {
          config: scenario1 as unknown as IConfiguration,
          breakDetails: {
            isWithReplacedAds: true,
            vast4Json: firstVast,
            adServerResponseLog: []
          }
        },
        {
          config: scenario2 as unknown as IConfiguration,
          breakDetails: {
            isWithReplacedAds: true,
            vast4Json: secondVast,
            adServerResponseLog: []
          }
        }
      ]);

      // assert
      expect(result.breakDetails.vast4Json.VAST.Ad).toMatchObject([
        { _attributes: { id: 'Spot_5' } },
        { _attributes: { id: 'Spot_2' } },
        { _attributes: { id: 'Spot_3' } },
        { _attributes: { id: 'Spot_4' } }
      ]);
    });
  });

  describe('multiple merge', () => {
    test('should return configs for one version', async () => {
      // arrange
      const firstVast = new VastBuilder().addSlot(true).addSlot(true).addSlot(true).build();
      const secondVast = new VastBuilder()
        .addSlot(true, 4)
        .addSlot(true, 5)
        .addSlot(true, 6)
        .build();

      // act
      const result = playlistMerger.mergeMultiple([
        [
          {
            config: scenario1 as unknown as IConfiguration,
            breakDetails: {
              isWithReplacedAds: true,
              vast4Json: firstVast,
              adServerResponseLog: []
            }
          },
          {
            config: scenario2 as unknown as IConfiguration,
            breakDetails: {
              isWithReplacedAds: true,
              vast4Json: secondVast,
              adServerResponseLog: []
            }
          }
        ]
      ]);

      // assert
      expect(result[0].breakDetails.vast4Json.VAST.Ad).toMatchObject([
        { _attributes: { id: 'Spot_1' } },
        { _attributes: { id: 'Spot_2' } },
        { _attributes: { id: 'Spot_3' } }
      ]);
      expect(result[1].breakDetails.vast4Json.VAST.Ad).toMatchObject([
        { _attributes: { id: 'Spot_4' } },
        { _attributes: { id: 'Spot_5' } },
        { _attributes: { id: 'Spot_6' } }
      ]);
    });

    test('should take first, fourth adslots to first config and third, fourth adslots to second config', async () => {
      // arrange
      const firstConfigVast = new VastBuilder().addSlot(false).addSlot(false).build();
      const secondConfigVast = new VastBuilder().addSlot(false, 5).addSlot(false, 6).build();

      const thirdConfigVast = new VastBuilder().addSlot(false, 3).addSlot(true, 4).build();
      const fourthConfigVast = new VastBuilder().addSlot(true, 7).addSlot(true, 8).build();

      // act
      const result = playlistMerger.mergeMultiple([
        [
          {
            config: scenario1 as unknown as IConfiguration,
            breakDetails: {
              isWithReplacedAds: true,
              vast4Json: firstConfigVast,
              adServerResponseLog: []
            }
          },
          {
            config: scenario2 as unknown as IConfiguration,
            breakDetails: {
              isWithReplacedAds: true,
              vast4Json: secondConfigVast,
              adServerResponseLog: []
            }
          }
        ],
        [
          {
            config: scenario2 as unknown as IConfiguration,
            breakDetails: {
              isWithReplacedAds: true,
              vast4Json: thirdConfigVast,
              adServerResponseLog: []
            }
          },
          {
            config: scenario1 as unknown as IConfiguration,
            breakDetails: {
              isWithReplacedAds: true,
              vast4Json: fourthConfigVast,
              adServerResponseLog: []
            }
          }
        ]
      ]);

      // assert
      expect(result[0].breakDetails.vast4Json.VAST.Ad).toMatchObject([
        { _attributes: { id: 'Spot_1' } },
        { _attributes: { id: 'Spot_4' } }
      ]);
      expect(result[1].breakDetails.vast4Json.VAST.Ad).toMatchObject([
        { _attributes: { id: 'Spot_7' } },
        { _attributes: { id: 'Spot_8' } }
      ]);
    });
  });
});
