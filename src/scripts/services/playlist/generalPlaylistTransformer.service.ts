import { Injectable } from '@nestjs/common';
import {
  AdVast4Normalized,
  IConfiguration,
  returnAsArrayEmpty,
  Vast4Normalized,
  LogLevel,
  logger
} from 'adpod-tools';
import { cloneDeepWith, cloneDeep } from 'lodash';
import { PlaylistMode } from '../../../models/playlistMode.model';
import { IDebugService } from '../../../libs';
import { Vmap, VmapAdBreakNormalized, VmapNormalized } from '../../../interfaces';
import { PrefetchType } from '../../../models/prefetch.model';

@Injectable()
export class GeneralPlaylistTransformer {
  constructor(private readonly debugService: IDebugService) {}

  public updateBreakType(
    isWithReplacedAds: boolean,
    playlistJson: AdVast4Normalized[]
  ): AdVast4Normalized[] {
    // eslint-disable-next-line consistent-return
    return cloneDeepWith(playlistJson, (value, index) => {
      if (index === '_attributes' && value?.breakId) {
        return {
          ...value,
          breakType: isWithReplacedAds ? 'dai' : 'mirrored'
        };
      }
    });
  }

  public async convertToVmap(
    vastJsons: Vast4Normalized[],
    configurations: IConfiguration[] = [],
    mode?: PlaylistMode,
    bids?: string[]
  ): Promise<VmapNormalized> {
    const adBreaks: VmapAdBreakNormalized[] = [];
    const debugMode = mode === PlaylistMode.debug;

    for (const [index, vastJson] of vastJsons.entries()) {
      const configuration = configurations[index];
      const bidId = configuration?.id || bids?.[index] || 'empty';

      const adBreak: VmapAdBreakNormalized = {
        _attributes: {
          timeOffset: configuration?.time || 'HH:MM:SS',
          breakType: vastJson.VAST.Ad.every((ad) => !!ad._attributes.linear)
            ? 'mirrored'
            : 'dai',
          breakId: bidId
        },
        'vmap:AdSource': {
          _attributes: {
            id: 'ads',
            allowMultipleAds: 'true',
            followRedirects: 'true'
          },
          'vmap:VASTAdData': {
            ...cloneDeep(vastJson)
          }
        }
      };

      if (debugMode) {
        adBreak.Debug = {
          _attributes: { breakId: bidId },
          AdRequestConnector: { _text: configuration?.breakConnector },
          Metadata: { _text: JSON.stringify(configuration?.metadata) },
          AdServerDetails: {
            _text: await this.debugService.getDebugDetails(vastJson, bidId)
          }
        };
      }

      adBreaks.push(adBreak);
    }

    return {
      'vmap:VMAP': {
        _attributes: {
          'xmlns:vmap': 'http://iab.net/videosuite/vmap',
          version: '1.0'
        },
        'vmap:AdBreak': adBreaks
      }
    };
  }

  public convertToVAST4(vastJsons: Vast4Normalized[], prefetch?: string): Vast4Normalized {
    return {
      VAST: {
        ...vastJsons[0].VAST,
        _attributes: {
          ...vastJsons[0].VAST._attributes,
          prefetch
        },
        Ad: vastJsons.flatMap((v) => returnAsArrayEmpty(v.VAST.Ad))
      }
    };
  }

  public replaceNotAllowedCharsInXml(
    playlist: string,
    mode: PrefetchType | PlaylistMode
  ): string {
    return mode === PrefetchType.nextDebugValid || mode === PlaylistMode.debugValid
      ? playlist.replaceAll('&', '&amp;')
      : playlist;
  }

  public injectPrefetchTimeToVmap = (
    vastJson: Vmap,
    prefetchTime: string | undefined
  ): Vmap => {
    if (!prefetchTime) {
      logger('REJECTED_INJECTING_PREFETCH_TIME_VMAP', {}, LogLevel.dev);
      return vastJson;
    }

    const vastJsonCopy: Vmap = cloneDeep(vastJson);

    // Vmap
    if (vastJsonCopy?.['vmap:VMAP']?._attributes) {
      vastJsonCopy['vmap:VMAP']._attributes.prefetch = prefetchTime;
      logger('INJECTED_PREFETCH_TIME_VMAP', {}, LogLevel.dev);
    }

    return vastJsonCopy;
  };
}
