import { Channel, LogLevel } from 'adpod-tools';
import { Injectable } from '@nestjs/common';
import logger from '../../libs/logging/logger';
import { URLParamsHelper } from '../adserver/urlHelper';
import { IDeapProfilesService } from './deapProfiles.service';

@Injectable()
export class CustomParamsGenerator {
  constructor(private readonly deapProfilesService: IDeapProfilesService) {}

  async generate(
    version: string,
    channel: Channel,
    custParams?: string,
    uid?: string,
    hasDeapProfiles?: boolean
  ): Promise<string> {
    const shouldFetchDeapProfiles = !!uid && hasDeapProfiles;

    const urlHelper = new URLParamsHelper(custParams ?? '', '&');

    if (shouldFetchDeapProfiles) {
      const deapProfiles = await this.deapProfilesService.getDeapProfiles(channel, uid);

      const defaultDeapProfiles = deapProfiles.defaultProfiles;
      const permutiveDeapProfiles = deapProfiles.permutiveProfiles;

      if (defaultDeapProfiles.length > 0) {
        defaultDeapProfiles.forEach((profile) => {
          urlHelper.add(profile, 'true');
        });
      } else {
        logger('DEAP_LACK_OF_DEAFAULT_PROFILES', { uid, defaultDeapProfiles }, LogLevel.dev);
      }

      if (permutiveDeapProfiles.length > 0) {
        permutiveDeapProfiles.forEach((profile) => {
          urlHelper.add('permutive', profile);
        });
      } else {
        logger(
          'DEAP_LACK_OF_PERMUTIVE_PROFILES',
          { uid, permutiveDeapProfiles },
          LogLevel.dev
        );
      }
    }

    urlHelper.add('v', version);

    return urlHelper.toString();
  }
}
