import { Injectable } from '@nestjs/common';
import { LogLevel } from 'adpod-tools';
import { TCString } from '@iabtcf/core';
import logger from '../../libs/logging/logger';

@Injectable()
export class TcfService {
  private isPersonalizedAdvertisingAllowed(gdprConsent?: string, gdpr?: string): boolean {
    if (!(gdprConsent && gdpr)) {
      return true;
    }

    if (+gdpr !== 1) {
      return true;
    }

    try {
      // [4] Use profiles to select personalised advertising
      return TCString.decode(gdprConsent).purposeConsents.has(4);
    } catch (err: unknown) {
      logger(
        `WARN_CONSENT_STRING_INVALID`,
        {
          gdprConsent,
          err
        },
        LogLevel.warn
      );
      return true;
    }
  }

  public deapProfilesEnabled(
    workerConfigDeapProfiles: boolean,
    gdprConsent?: string,
    gdpr?: string
  ): boolean {
    const personalizedAdvertisingAllowed = this.isPersonalizedAdvertisingAllowed(
      gdprConsent,
      gdpr
    );

    logger(
      `${personalizedAdvertisingAllowed ? '' : 'WARN_'}CONSENT_STRING_PERSONALIZED_ADS_STATUS`,
      {
        personalizedAdvertisingAllowed,
        workerConfigDeapProfiles
      },
      personalizedAdvertisingAllowed ? LogLevel.debug : LogLevel.warn
    );

    return workerConfigDeapProfiles && personalizedAdvertisingAllowed;
  }
}
