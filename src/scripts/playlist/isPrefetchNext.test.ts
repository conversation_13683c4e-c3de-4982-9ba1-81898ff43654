import { PrefetchType } from '../../models/prefetch.model';
import { isPrefetchNext } from './isPrefetchNext';

describe('isPrefetchNext test suite', () => {
  test('is a function', () => {
    expect(typeof isPrefetchNext).toBe('function');
  });
  test('should return true for mode = next', () => {
    expect(isPrefetchNext(PrefetchType.next)).toBe(true);
  });
  test('should return true for mode = nextMirrored', () => {
    expect(isPrefetchNext(PrefetchType.nextMirrored)).toBe(true);
  });
  test('should return true for mode = nextMixed', () => {
    expect(isPrefetchNext(PrefetchType.nextMixed)).toBe(true);
  });
  test('should return true for mode = nextDebug', () => {
    expect(isPrefetchNext(PrefetchType.nextDebug)).toBe(true);
  });
  test('should return false for mode = hours1', () => {
    expect(isPrefetchNext(PrefetchType.hours1)).toBe(false);
  });
  test('should return false for mode = hours1Debug', () => {
    expect(isPrefetchNext(PrefetchType.hours1Debug)).toBe(false);
  });
  test('should return false for mode = hours1Mirrored', () => {
    expect(isPrefetchNext(PrefetchType.hours1Mirrored)).toBe(false);
  });
  test('should return false for mode = hours1Mixed', () => {
    expect(isPrefetchNext(PrefetchType.hours1Mixed)).toBe(false);
  });
  test('should return false for mode = hours24hDebug', () => {
    expect(isPrefetchNext(PrefetchType.hours24hDebug)).toBe(false);
  });
  test('should return false for mode = hours24hMirrored', () => {
    expect(isPrefetchNext(PrefetchType.hours24hMirrored)).toBe(false);
  });
  test('should return false for mode = hours24hMixed', () => {
    expect(isPrefetchNext(PrefetchType.hours24hMixed)).toBe(false);
  });
  test('should return false for mode = minutes30', () => {
    expect(isPrefetchNext(PrefetchType.minutes30)).toBe(false);
  });
  test('should return false for mode = minutes30Debug', () => {
    expect(isPrefetchNext(PrefetchType.minutes30Debug)).toBe(false);
  });
  test('should return false for mode = minutes30Mirrored', () => {
    expect(isPrefetchNext(PrefetchType.minutes30Mirrored)).toBe(false);
  });
  test('should return false for mode = minutes30Mixed', () => {
    expect(isPrefetchNext(PrefetchType.minutes30Mixed)).toBe(false);
  });
});
