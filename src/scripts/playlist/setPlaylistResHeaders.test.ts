import { FastifyReply } from 'fastify';
import { setPlaylistResHeaders } from './setPlaylistResHeaders';
import { validators } from '../../EnvValidation/envalidConfig';

describe('setPlaylistResHeaders test suite', () => {
  let savedHeaders = {};
  const res = {
    headers: (headersToSet: any) => {
      savedHeaders = headersToSet;
    },
    getHeaders: () => savedHeaders
  } as FastifyReply;
  test('is a function', () => {
    expect(typeof setPlaylistResHeaders).toBe('function');
  });
  test('should properly set headers, isEmptyVast = true', () => {
    void setPlaylistResHeaders(res, true);
    expect(res.getHeaders()).toMatchObject({
      'Content-Type': 'text/xml',
      'X-TVN-ADPOD-MID': validators.HOSTNAME,
      'X-TVN-ADPOD-RESULT': 'EMPTY_VAST'
    });
  });
  test('should properly set headers, isEmptyVast = false', () => {
    void setPlaylistResHeaders(res, false);
    expect(res.getHeaders()).toMatchObject({
      'Content-Type': 'text/xml',
      'X-TVN-ADPOD-MID': validators.HOSTNAME,
      'X-TVN-ADPOD-RESULT': 'VAST'
    });
  });
});
