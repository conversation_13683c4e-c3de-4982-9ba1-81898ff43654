import { isPlaylistModeDefault } from './isPlaylistModeDefault';
import { PlaylistMode } from '../../models/playlistMode.model';

describe('isPlaylistModeDefault test suite', () => {
  test('is a function', () => {
    expect(typeof isPlaylistModeDefault).toBe('function');
  });
  test('should return true on valid value', () => {
    expect(isPlaylistModeDefault(PlaylistMode.default)).toBe(true);
  });
  test('should return false on invalid value', () => {
    expect(isPlaylistModeDefault(PlaylistMode.mirrored)).toBe(false);
  });
});
