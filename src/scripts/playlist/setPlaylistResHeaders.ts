import { FastifyReply } from 'fastify';
import { Header } from '../../models/header.model';
import { HeaderValue } from '../../models/headerValue.model';
import { validators } from '../../EnvValidation/envalidConfig';

export const setPlaylistResHeaders = async (
  res: FastifyReply,
  isEmptyVast: boolean,
  xTvnLinksResponseProto?: string | string[],
  xForwardedProto?: string | string[],
  protocol?: string
): Promise<void> => {
  // eslint-disable-next-line no-void
  void res.headers({
    [Header.contentType]: HeaderValue.xml,
    [Header.adpodMid]: validators.HOSTNAME,
    [Header.adpodResult]: isEmptyVast ? HeaderValue.emptyVast : HeaderValue.properVast,
    'x-tvn-links-response-proto': xTvnLinksResponseProto,
    'x-forwarded-proto': xForwardedProto,
    protocol
  });
};
