import { IncomingHttpHeaders } from 'http';
import { LogLevel, returnAsArrayEmpty } from 'adpod-tools';
import { IPlaylistInfoItem, IRequestLog } from '../../interfaces';
import logger from '../../libs/logging/logger';
import { validators } from '../../EnvValidation/envalidConfig';
import { SnowFlakeEnabledConfig } from '../../models/workerConfig';

export const sendResponseLogsToSnowflake = (
  requestLog: IRequestLog,
  responseTime: number,
  ip: string | undefined,
  headers: IncomingHttpHeaders,
  emptyVastReason: string | null,
  playlistInfo: (IPlaylistInfoItem | null)[] | IPlaylistInfoItem | null,
  snowFlakeEnabledConfigs: SnowFlakeEnabledConfig[],
  version?: string,
  sessionId?: string,
  ipLogging?: boolean
): IRequestLog & {
  requestDurationMs: string;
  ip: string | undefined;
  headers: IncomingHttpHeaders;
  emptyVastReason: string | null;
  breaksIds: string[];
  breaksCount: number;
  allAdSlotsCount: number;
  replacedSlotsCount: number;
} => {
  const playlistInfoArr = returnAsArrayEmpty(playlistInfo);

  const breaksIds: string[] = [];
  const breaksCount = playlistInfoArr.length;
  let allAdSlotsCount = 0;
  let replacedSlotsCount = 0;

  if (breaksCount) {
    playlistInfoArr.forEach(async (curr) => {
      if (curr) {
        breaksIds.push(curr.bid);
        allAdSlotsCount += curr.breakAllAdsCount;
        replacedSlotsCount += curr.breakDaiPlaylistAdsCount;

        const snowflakeResponseData = {
          ti: 'pl',
          et: 'apm_logger',
          eid: 'apm_response',
          ev: '1_0_0',
          ed: {
            session_id: sessionId,
            bid: curr.bid,
            no_dai: curr.breakDaiPlaylistAdsCount,
            v: requestLog.configVersion,
            ch: requestLog.channel,
            ip: ipLogging ? ip : '',
            dai_ads: curr.breakAds
              .filter((a) => a.t === 'DAI')
              .map(
                ({
                  id,
                  universalAdId,
                  adServer: { fw_slotImpressionEvent, fw_slotEndEvent }
                }) => ({
                  id,
                  universalAdId,
                  fw_slotImpressionEvent,
                  fw_slotEndEvent
                })
              )
          },
          deuid: requestLog.requestMacroParams.uid,
          ts: Date.now(),
          env: validators.NODE_ENV,
          ctx: {},
          appVersion: process.env.npm_package_version
        };

        const isLoggingEnabledForConfig = snowFlakeEnabledConfigs.find(
          (config) => config.version === version && config.channel === requestLog.channel
        );
        if (isLoggingEnabledForConfig) {
          logger(
            'SNOWFLAKE_RESPONSE_OK_INPUT_RAW',
            { input: snowflakeResponseData },
            LogLevel.snowflake
          );
        }
      }
    });
  }

  return {
    ...requestLog,
    requestDurationMs: responseTime?.toFixed(0),
    ip,
    headers,
    emptyVastReason,
    breaksIds,
    breaksCount,
    allAdSlotsCount,
    replacedSlotsCount
  };
};
