import { hasCorrectDateTimeFormat } from './hasCorrectDateTimeFormat';

describe('hasCorrectDateTimeFormat test suite', () => {
  test('correct format, correct values', () => {
    expect(hasCorrectDateTimeFormat('2021-06-29T14:44:57+02:00')).toBeTruthy();
    expect(hasCorrectDateTimeFormat('0000-01-01T14:44:57+02:00')).toBeTruthy();
    expect(hasCorrectDateTimeFormat('9999-12-31T14:44:57+02:00')).toBeTruthy();
  });

  // correct format, incorrect values
  test('correct format, incorret day value', () => {
    expect(hasCorrectDateTimeFormat('2021-06-32:44:57+02:00')).toBeFalsy();
  });

  test('correct format, incorret month value', () => {
    expect(hasCorrectDateTimeFormat('2021-13-10T14:44:57+02:00')).toBeFalsy();
  });

  test('correct format, incorret time hour value', () => {
    expect(hasCorrectDateTimeFormat('2021-13-10T25:44:57+02:00')).toBeFalsy();
  });

  test('correct format, incorret time min value', () => {
    expect(hasCorrectDateTimeFormat('2021-13-10T14:60:57+02:00')).toBeFalsy();
  });

  test('correct format, incorret time offset value', () => {
    expect(hasCorrectDateTimeFormat('2021-06-29T14:44:57+99:00')).toBeFalsy();
  });

  // separators
  test('wrong date separators', () => {
    expect(hasCorrectDateTimeFormat('2021 06 29T14:44:57+02:00')).toBeFalsy();
    expect(hasCorrectDateTimeFormat('2021/06/29T14:44:57+02:00')).toBeFalsy();
  });

  test('wrong time separators', () => {
    expect(hasCorrectDateTimeFormat('2021-06-29T14 44 57+02:00')).toBeFalsy();
  });

  test('wrong date - time separator', () => {
    expect(hasCorrectDateTimeFormat('2021-06-29 14:44:57+02:00')).toBeFalsy();
  });

  test('wrong time - time offset separator', () => {
    expect(hasCorrectDateTimeFormat('2021-06-29T14:44:57 02:00')).toBeFalsy();
    expect(hasCorrectDateTimeFormat('2021-06-29T14:44:57-02:00')).toBeFalsy();
  });

  test('wrong time offset separator', () => {
    expect(hasCorrectDateTimeFormat('2021-06-29T14:44:57+0200')).toBeFalsy();
  });

  test('wrong time offset format', () => {
    expect(hasCorrectDateTimeFormat('2021-06-29T14:44:57+2:00')).toBeFalsy();
  });

  test('wrong time offset value', () => {
    expect(hasCorrectDateTimeFormat('2021-06-29T14:44:57+99:00')).toBeFalsy();
  });

  // incorrect format
  test('arg not provided', () => {
    expect(hasCorrectDateTimeFormat()).toBeFalsy();
  });

  test('empty string provided', () => {
    expect(hasCorrectDateTimeFormat('')).toBeFalsy();
  });

  test('undefined provided', () => {
    expect(hasCorrectDateTimeFormat(undefined)).toBeFalsy();
  });

  test('only date provided', () => {
    expect(hasCorrectDateTimeFormat('2021-06-29')).toBeFalsy();
  });

  test('date without day provided', () => {
    expect(hasCorrectDateTimeFormat('2021-06T14:44:57+02:00')).toBeFalsy();
  });

  test('incorrect date format (year)', () => {
    expect(hasCorrectDateTimeFormat('21-06-29T14:44:57+02:00')).toBeFalsy();
  });

  test('incorrect date format (month)', () => {
    expect(hasCorrectDateTimeFormat('2021-6-29T14:44:57+02:00')).toBeFalsy();
  });

  test('incorrect date format (day)', () => {
    expect(hasCorrectDateTimeFormat('2021-06-1T14:44:57+02:00')).toBeFalsy();
  });

  test('incorrect time format (hour)', () => {
    expect(hasCorrectDateTimeFormat('2021-06-1T1:44:57+02:00')).toBeFalsy();
  });

  test('incorrect time format (minutes)', () => {
    expect(hasCorrectDateTimeFormat('2021-06-1T14:1:57+02:00')).toBeFalsy();
  });

  test('incorrect time format (seconds)', () => {
    expect(hasCorrectDateTimeFormat('2021-06-1T14:44:7+02:00')).toBeFalsy();
  });
});
