import dayjs from 'dayjs';
import { getPastDates } from './getPastDates';

describe('getPastDates test suite', () => {
  const today = new Date(2021, 9, 5); // 2021-10-05
  const get3DaysFromThePast = getPastDates(3, dayjs(today));

  test('should returns yesterday date for 1 passed as first argument', async () => {
    expect(getPastDates(1, dayjs(today))).toEqual(['20211004']);
  });

  test('should returns array with 3 dates for 3 passed as first argument', async () => {
    expect(get3DaysFromThePast).toEqual(['20211004', '20211003', '20211002']);
  });

  test('should return sorted array', async () => {
    expect(get3DaysFromThePast).not.toEqual(['20211002', '20211003', '20211004']);
  });

  test('should return today and yesterday for countDays=2 and startDate=tomorrow', async () => {
    expect(getPastDates(2, dayjs(today).add(1, 'day'))).toEqual(['20211005', '20211004']);
  });
});
