import { splitTimeData } from './splitTimeData';

describe('splitTimeData test suite', () => {
  test('splitTimeData is a function', () => {
    expect(typeof splitTimeData).toEqual('function');
  });

  test('prepare split time data, 1', () => {
    expect(splitTimeData('2020-09-24T11:23:03+02:00')).toEqual({
      year: 2020,
      month: 9,
      day: 24,
      hour: 11,
      minutes: 23,
      seconds: 3,
      zone: '+02:00'
    });
  });

  test('prepare split time data, 2', () => {
    expect(splitTimeData('2023-10-24T11:23:03+02:00')).toEqual({
      year: 2023,
      month: 10,
      day: 24,
      hour: 11,
      minutes: 23,
      seconds: 3,
      zone: '+02:00'
    });
  });

  test('prepare split time data, invalid 1', () => {
    expect(splitTimeData('202-10-24T11:23:03+02:00')).toBe(null);
  });

  test('prepare split time data, invalid 2', () => {
    expect(splitTimeData('')).toBe(null);
  });
});
