import { ITimeData } from '../../interfaces';

// Example input "2020-09-24T11:23:03+02:00"
export const splitTimeData = (time: string): ITimeData | null => {
  if (!time || time.length < 25) return null;

  const newTimeData: ITimeData = {
    year: parseInt(time.substr(0, 4)),
    month: parseInt(time.substr(5, 7)),
    day: parseInt(time.substr(8, 10)),
    hour: parseInt(time.substr(11, 13)),
    minutes: parseInt(time.substr(14, 16)),
    seconds: parseInt(time.substr(17, 19)),
    zone: time.substr(19, 22)
  };

  return newTimeData;
};
