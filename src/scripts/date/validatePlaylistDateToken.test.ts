import dayjs from 'dayjs';
import { sha256 } from 'js-sha256';
import { validatePlaylistDateToken } from './validatePlaylistDateToken';

describe('validatePlaylistDateToken test suite', () => {
  test('validatePlaylistDateToken is a function', () => {
    expect(typeof validatePlaylistDateToken).toBe('function');
  });

  test('should return true for appropriate input', () => {
    expect(validatePlaylistDateToken(sha256(dayjs().format('YYYYMM')))).toBe(true);
  });

  test('should return false for inappropriate input', () => {
    expect(validatePlaylistDateToken(sha256('198105'))).toBe(false);
  });
});
