import dayjs from 'dayjs';

/*
  Returns an array of dates from the past.
  input: getPastDates(2, dayjs(new Date(2021, 9, 5)))
  output: [20211004, 20211003]
*/
export const getPastDates = (countDays: number, startDate: dayjs.Dayjs): string[] => {
  const dates: string[] = [];
  for (let a = countDays; a > 0; a--) {
    dates.push(startDate.subtract(a, 'day').format('YYYYMMDD'));
  }

  return dates.reverse();
};
