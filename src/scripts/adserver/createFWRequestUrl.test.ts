import { createFWRequestUrl } from './createFWRequestUrl';

describe('createFWRequestUrl tests', () => {
  const baseURL = `https://5e529.v.fwmrm.net/ad/g/1?prof=386345%3ADNI_IT_HbbTV_SSAI_live&nw=386345&metr=1031&caid=ADDRESSABLE_TV_DUMMY_ASSET&asnw=386345&csid=ADDRESSABLE_TV_ITALY&ssid=16708015&vprn=97288861&vip=*************&vdur=30&resp=vast4&flag=+scpv+emcr+amcb+slcb+aeti;test=mxf;ch=TTV;pod=7515640569244321;p=1;_fw_vcid2=2b79408f-127d-480b-bdbf-52bdd9528ca0&_fw_h_user_agent=Mozilla%2F5.0%20(Macintosh%3B%20Intel%20Mac%20OS%20X%2010_15_7);ptgt=a&tpos=0&slau=Preroll%20Spot&_fw_gdpr=1`;

  const baseURLWithDuration = `https://5e529.v.fwmrm.net/ad/g/1?prof=386345%3ADNI_IT_HbbTV_SSAI_live&nw=386345&metr=1031&caid=ADDRESSABLE_TV_DUMMY_ASSET&asnw=386345&csid=ADDRESSABLE_TV_ITALY&ssid=16708015&vprn=97288861&vip=*************&vdur=30&resp=vast4&flag=+scpv+emcr+amcb+slcb+aeti;test=mxf;ch=TTV;pod=7515640569244321;p=1;_fw_vcid2=2b79408f-127d-480b-bdbf-52bdd9528ca0&_fw_h_user_agent=Mozilla%2F5.0%20(Macintosh%3B%20Intel%20Mac%20OS%20X%2010_15_7);ptgt=a&tpos=0&slau=Preroll%20Spot`;

  const baseURLWithProfiles = `https://5e529.v.fwmrm.net/ad/g/1?prof=386345%3ADNI_IT_HbbTV_SSAI_live&nw=386345&metr=1031&caid=ADDRESSABLE_TV_DUMMY_ASSET&asnw=386345&csid=ADDRESSABLE_TV_ITALY&ssid=16708015&vprn=97288861&vip=*************&vdur=30&resp=vast4&flag=+scpv+emcr+amcb+slcb+aeti;test=mxf;ch=TTV;pod=7515640569244321;p=1;_fw_vcid2=2b79408f-127d-480b-bdbf-52bdd9528ca0&_fw_h_user_agent=Mozilla%2F5.0%20(Macintosh%3B%20Intel%20Mac%20OS%20X%2010_15_7);ptgt=a&tpos=0&slau=Preroll%20Spot&deap_profile_sample=true&deap_profile_sample1_0_0_0=true&deap_profile_your_dad=true&deap_profile_your_mum=true`;

  const baseURLWithCustParams = `https://5e529.v.fwmrm.net/ad/g/1?prof=386345%3ADNI_IT_HbbTV_SSAI_live&nw=386345&metr=1031&caid=ADDRESSABLE_TV_DUMMY_ASSET&asnw=386345&csid=ADDRESSABLE_TV_ITALY&ssid=16708015&vprn=97288861&vip=*************&vdur=30&resp=vast4&flag=+scpv+emcr+amcb+slcb+aeti;test=mxf;ch=TTV;pod=7515640569244321;p=1;_fw_vcid2=2b79408f-127d-480b-bdbf-52bdd9528ca0&_fw_h_user_agent=Mozilla%2F5.0%20(Macintosh%3B%20Intel%20Mac%20OS%20X%2010_15_7);ptgt=a&tpos=0&slau=Preroll%20Spot&param=foo&param2=foo2`;

  const baseURLWithCustParamsAndProfiles = `https://5e529.v.fwmrm.net/ad/g/1?prof=386345%3ADNI_IT_HbbTV_SSAI_live&nw=386345&metr=1031&caid=ADDRESSABLE_TV_DUMMY_ASSET&asnw=386345&csid=ADDRESSABLE_TV_ITALY&ssid=16708015&vprn=97288861&vip=*************&vdur=30&resp=vast4&flag=+scpv+emcr+amcb+slcb+aeti;test=mxf;ch=TTV;pod=7515640569244321;p=1;_fw_vcid2=2b79408f-127d-480b-bdbf-52bdd9528ca0&_fw_h_user_agent=Mozilla%2F5.0%20(Macintosh%3B%20Intel%20Mac%20OS%20X%2010_15_7);ptgt=a&tpos=0&slau=Preroll%20Spot&param=foo&param2=foo2&deap_profile_sample=true&deap_profile_sample1_0_0_0=true&deap_profile_your_dad=true&deap_profile_your_mum=true`;

  test('should return base URL with duration if cust_params provided', () => {
    expect(createFWRequestUrl(baseURL, ' 0')).toEqual(baseURLWithDuration);
  });

  test('should return URL with encoded cust_params', () => {
    expect(createFWRequestUrl(baseURL, '0', undefined, 'param%3Dfoo%26param2%3Dfoo2')).toEqual(
      baseURLWithCustParams
    );
  });

  test('should return URL with decoded cust_params', () => {
    expect(createFWRequestUrl(baseURL, '0', undefined, 'param=foo&param2=foo2')).toEqual(
      baseURLWithCustParams
    );
  });

  test('should return URL with profiles', () => {
    expect(
      createFWRequestUrl(
        baseURL,
        '0',
        undefined,
        'deap_profile_sample=true&deap_profile_sample1_0_0_0=true&deap_profile_your_dad=true&deap_profile_your_mum=true'
      )
    ).toEqual(baseURLWithProfiles);
  });

  test('should return URL with profiles and cust_params decoded', () => {
    expect(
      createFWRequestUrl(
        baseURL,
        '0',
        undefined,
        'param=foo&param2=foo2&deap_profile_sample=true&deap_profile_sample1_0_0_0=true&deap_profile_your_dad=true&deap_profile_your_mum=true'
      )
    ).toEqual(baseURLWithCustParamsAndProfiles);
  });

  test('should return URL with profiles and cust_params partially encoded', () => {
    expect(
      createFWRequestUrl(
        baseURL,
        '0',
        undefined,
        'param%3Dfoo%26param2%3Dfoo2&deap_profile_sample=true&deap_profile_sample1_0_0_0=true&deap_profile_your_dad=true&deap_profile_your_mum=true'
      )
    ).toEqual(baseURLWithCustParamsAndProfiles);
  });
});
