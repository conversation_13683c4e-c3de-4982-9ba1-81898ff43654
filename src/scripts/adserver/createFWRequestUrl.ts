import { URLParamsHelper } from './urlHelper';

export const createFWRequestUrl = (
  baseConfigUrl: string,
  gdpr?: string,
  gdprConsent?: string,
  custParams?: string
): string => {
  const helper = new URLParamsHelper(baseConfigUrl, '&');

  if (custParams) {
    const helper2 = new URLParamsHelper(custParams);
    helper.addQueries(helper2);
  }

  if (gdpr === '1') {
    helper.delete('_fw_is_lat');
  }

  if (!gdpr || gdpr === '0' || !gdprConsent) {
    helper.delete('_fw_gdpr').delete('_fw_gdpr_consent');
  }

  return helper.toString();
};
