import { createGAMRequestUrl } from './createGAMRequestUrl';

describe('createGAMRequestUrl tests', () => {
  const gamURLDefaultCustPrams = '&cust_params=adid%3D123%26o%3D123%26ct%3Dlinear%26ch%3DTTV';
  const gamURLDecodedCustPrams = '&adid=123&o=123&ct=linear&ch=TTV';

  const gamURLWithoutCustPrams =
    'https://pubads.g.doubleclick.net/gampad/live/ads?sz=640x480&iu=/65073904/hbbtv&impl=s&gdfp_req=1&env=vp&output=xml_vast4&unviewed_position_start=1&url=TBA&description_url=TBA&correlator=$RAND_8&scor=$RAND_8&ss_req=1&ip=$DEVICE_IP';

  const npa = '&npa=$GDPR_CONSENT';

  const gamOtherParams = '&pod=123&min_ad_duration=15&max_ad_duration=15';

  const gamBaseURL = `${gamURLWithoutCustPrams}${npa}${gamOtherParams}${gamURLDefaultCustPrams}`;

  test('should return base URL if cust_params and uid not provided', () => {
    const providerRequest = {
      headers: null as any,
      gdpr: '0'
    };
    expect(createGAMRequestUrl(gamBaseURL, providerRequest)).toEqual(
      `${gamURLWithoutCustPrams}${npa}${gamOtherParams}${gamURLDecodedCustPrams}`
    );
  });

  describe('extends base URL cust_params', () => {
    test('should extends base URL cust_params if request cust_params (decoded) are provided 1', () => {
      const providerRequest = {
        headers: null as any,
        gdpr: '0',
        custParams: 'paramOne=valueOne'
      };

      expect(createGAMRequestUrl(gamBaseURL, providerRequest)).toEqual(
        `${gamURLWithoutCustPrams}${npa}${gamOtherParams}${gamURLDecodedCustPrams}&paramOne=valueOne`
      );
    });
    test('should extends base URL cust_params if request cust_params (decoded) are provided 2', () => {
      const providerRequest = {
        headers: null as any,
        gdpr: '0',
        custParams: 'paramOne=valueOne&paramTwo=valueTwo'
      };

      expect(createGAMRequestUrl(gamBaseURL, providerRequest)).toEqual(
        `${gamURLWithoutCustPrams}${npa}${gamOtherParams}${gamURLDecodedCustPrams}&paramOne=valueOne&paramTwo=valueTwo`
      );
    });
    test('should extends base URL cust_params if request cust_params (encoded) are provided 1', () => {
      const providerRequest = {
        headers: null as any,
        gdpr: '0',
        custParams: 'paramOne%3DvalueOne'
      };
      expect(createGAMRequestUrl(gamBaseURL, providerRequest)).toEqual(
        `${gamURLWithoutCustPrams}${npa}${gamOtherParams}${gamURLDecodedCustPrams}&paramOne=valueOne`
      );
    });
    test('should extends base URL cust_params if request cust_params (encoded) are provided 2', () => {
      const providerRequest = {
        headers: null as any,
        gdpr: '0',
        custParams: 'paramOne%3DvalueOne%26paramTwo%3DvalueTwo'
      };
      expect(createGAMRequestUrl(gamBaseURL, providerRequest)).toEqual(
        `${gamURLWithoutCustPrams}${npa}${gamOtherParams}${gamURLDecodedCustPrams}&paramOne=valueOne&paramTwo=valueTwo`
      );
    });
  });

  test('should add ppid to base URL', () => {
    const providerRequest = {
      headers: null as any,
      gdpr: '0',
      uid: '123'
    };
    expect(createGAMRequestUrl(gamBaseURL, providerRequest)).toEqual(
      `${gamURLWithoutCustPrams}${npa}${gamOtherParams}${gamURLDecodedCustPrams}&ppid=123`
    );
  });

  test('should not add cust_params uid to URL as ppid', () => {
    const providerRequest = {
      headers: null as any,
      gdpr: '0',
      custParams: 'uid=uidValue',
      uid: '123'
    };
    expect(createGAMRequestUrl(gamBaseURL, providerRequest)).toEqual(
      `${gamURLWithoutCustPrams}${npa}${gamOtherParams}${gamURLDecodedCustPrams}&uid=uidValue&ppid=123`
    );
  });

  test('should add ppid if multiple cust_params (decoded) are provided', () => {
    const providerRequest = {
      headers: null as any,
      gdpr: '0',
      custParams: 'paramOne=valueOne&paramTwo=valueTwo',
      uid: '123'
    };
    expect(createGAMRequestUrl(gamBaseURL, providerRequest)).toEqual(
      `${gamURLWithoutCustPrams}${npa}${gamOtherParams}${gamURLDecodedCustPrams}&paramOne=valueOne&paramTwo=valueTwo&ppid=123`
    );
  });

  test('should add ppid if multiple cust_params (encoded) are provided', () => {
    const providerRequest = {
      headers: null as any,
      gdpr: '0',
      custParams: 'paramOne%3DvalueOne%26paramTwo%3DvalueTwo',
      uid: '123'
    };
    expect(createGAMRequestUrl(gamBaseURL, providerRequest)).toEqual(
      `${gamURLWithoutCustPrams}${npa}${gamOtherParams}${gamURLDecodedCustPrams}&paramOne=valueOne&paramTwo=valueTwo&ppid=123`
    );
  });

  test('should add cust_params for base URL without cust_params', () => {
    const providerRequest = {
      headers: null as any,
      gdpr: '0',
      custParams: 'paramOne%3DvalueOne%26paramTwo%3DvalueTwo',
      uid: '123'
    };
    expect(createGAMRequestUrl(gamURLWithoutCustPrams, providerRequest)).toEqual(
      `${gamURLWithoutCustPrams}&paramOne=valueOne&paramTwo=valueTwo&ppid=123`
    );
  });

  test('should remove gdpr and gdpr_consent', () => {
    const providerRequest = {
      headers: null as any
    };
    const url = `${gamBaseURL}&gdpr=ABC123&gdpr_consent=DEF456`;
    expect(createGAMRequestUrl(url, providerRequest)).toEqual(
      `${gamURLWithoutCustPrams}${npa}${gamOtherParams}${gamURLDecodedCustPrams}`
    );
  });

  test('should remove npa', () => {
    const providerRequest = {
      headers: null as any,
      gdpr: '1'
    };
    expect(createGAMRequestUrl(gamBaseURL, providerRequest)).toEqual(
      `${gamURLWithoutCustPrams}${gamOtherParams}${gamURLDecodedCustPrams}`
    );
  });

  test('should add cust_params (decoded) when request do not have cust_params', () => {
    const providerRequest = {
      headers: null as any,
      gdpr: '0',
      custParams: 'adid=123&o=123&ct=linear&ch=TTV'
    };
    expect(
      createGAMRequestUrl(`${gamURLWithoutCustPrams}${npa}${gamOtherParams}`, providerRequest)
    ).toEqual(`${gamURLWithoutCustPrams}${npa}${gamOtherParams}${gamURLDecodedCustPrams}`);
  });

  test('should add cust_params (encoded) and decode them when request do not have cust_params', () => {
    const providerRequest = {
      headers: null as any,
      gdpr: '0',
      custParams: 'adid%3D123%26o%3D123%26ct%3Dlinear%26ch%3DTTV'
    };
    expect(
      createGAMRequestUrl(`${gamURLWithoutCustPrams}${npa}${gamOtherParams}`, providerRequest)
    ).toEqual(`${gamURLWithoutCustPrams}${npa}${gamOtherParams}${gamURLDecodedCustPrams}`);
  });
});
