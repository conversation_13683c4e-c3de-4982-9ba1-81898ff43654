import { isEncoded } from 'adpod-tools';

/**
 * Helper class to work with URL parameters.
 * It does not validate the URL.
 * It is assumed that the input 'url' is valid.
 * It supports both encoded and decoded parameters.
 *
 * Five possibilities are handled:
 * 1. It is a full URL ("www.website.com/path?param1=value1&param2=value2")
 * 2. It is a 'partial' URL ("www.website.com/path")
 * 3. It is a query string ("param1=value1&param2=value2")
 * 4. It is empty string ("")
 * 5. It is a query string with encoded parameters ("param1%3Dvalue1%26param2%3Dvalue2")

 *
 * @param url - The string to work with. Defaults to an empty string.
 * @param sep - The separator to use. Defaults to an "/".
 * @returns - Returns an instance of URLParamsHelper.
 */
export class URLParamsHelper {
  private _query = new Map<string, string>();

  private _shouldBeEncoded: boolean;

  private __url: string;

  private _withSuffix = false;

  private _parsable = false;

  private _sep: string;

  private _tempDuplicatedKeyPrefix: string = 'temp_duplicated_key_';

  constructor(url = '', sep?: string) {
    let queryLessUrl: string = '';
    let urlQuery: string = '';

    this._parsable = URL.canParse(url);

    if (this._parsable) {
      // Case 1) or 2)
      if (url.includes('?')) {
        [queryLessUrl, urlQuery] = url.split('?', 2); // Case 1)
      } else {
        queryLessUrl = url; // Case 2)
      }
    } else {
      const tryDecoded = decodeURIComponent(url);
      if (URL.canParse(tryDecoded)) {
        this._parsable = true;
        if (tryDecoded.includes('?')) {
          [queryLessUrl, urlQuery] = tryDecoded.split('?', 2); // Case 1) encoded
        } else {
          queryLessUrl = tryDecoded; // Case 2) encoded
        }
      } else {
        urlQuery = tryDecoded; // Case 3) or 4)
      }
    }

    this.__url = queryLessUrl;

    this._shouldBeEncoded = this.isFullyEncoded(url) || isEncoded(sep);

    if (sep === undefined) {
      this._sep = this.extractSeparator(urlQuery);
    } else {
      this._sep = isEncoded(sep) ? decodeURIComponent(sep) : sep;
    }

    if (urlQuery) {
      this.populateQueryMap(urlQuery);
    }
  }

  private extractSeparator(urlQuery: string): string {
    const pattern = /(?<__sep>[^\w])(\w+=\w+)/;

    const match = urlQuery.match(pattern);
    return match?.groups?.__sep ?? '/';
  }

  private populateQueryMap(urlQuery: string): void {
    const params = urlQuery.split(this._sep);

    if (params[params.length - 1] === '') {
      params.pop();
      this._withSuffix = true;
    }

    const pattern = /(?<key>\w+)=(?<value>.*)/;
    params.forEach((param, index) => {
      const match = param.match(pattern);
      if (!match || !match.groups) {
        return;
      }
      if (this._query.has(match.groups.key)) {
        this._query.set(
          `${this._tempDuplicatedKeyPrefix}${index}`,
          JSON.stringify({
            originalName: match.groups.key,
            val: match.groups.value
          })
        );
      } else {
        this._query.set(match.groups.key, match.groups.value);
      }
    });
  }

  /**
   * Removes a URL parameter from the URL.
   *
   * @param param - The name of the parameter to remove.
   * @return - Returns the instance of URLParamsHelper for method chaining.
   */
  delete(param: string): URLParamsHelper {
    this._query.delete(param);
    return this;
  }

  /**
   * Sets a URL parameter with a new value only if the parameter already exists.
   *
   * @param param - The name of the parameter to replace.
   * @param value - The new value for the parameter. Defaults to an empty string.
   * @return Returns the instance of URLParamsHelper for method chaining.
   */
  set(param: string, value: string): URLParamsHelper {
    if (this._query.has(param)) {
      this._query.set(param, value);
    }
    return this;
  }

  /**
   * Adds a URL parameter and its value to the URL.
   *
   * @param param - The name of the parameter to add.
   * @param value - The value of the parameter.
   * @param encodeValue - Whether to encode the value. Defaults to false.
   * @return - Returns the instance of URLParamsHelper for method chaining.
   */
  add(param: string, value: string | number | boolean, encodeValue = false): URLParamsHelper {
    const newValue =
      encodeValue && !isEncoded(value.toString())
        ? encodeURIComponent(value)
        : value.toString();

    this._query.set(param, newValue);
    return this;
  }

  /**
   * Adds a URL parameter and its value to the URL if the value is nonNullable.
   *
   * @param param - The name of the parameter to add.
   * @param value - The value of the parameter.
   * @param encodeValue - Whether to encode the value.
   * @return Returns the instance of URLParamsHelper for method chaining.
   */
  addMaybe(
    param: string,
    value: string | number | boolean | undefined,
    encodeValue = false
  ): URLParamsHelper {
    if (value) {
      this.add(param, value, encodeValue);
    }
    return this;
  }

  /**
   * Adds a URL parameter and its value to the URL if the value is nonNullable.
   * It does not encode the value. It adds the value as is.
   *
   * @param param - The name of the parameter to add.
   * @param value - The value of the parameter.
   * @return Returns the instance of URLParamsHelper for method chaining.
   */
  addMaybeRaw(param: string, value: string | undefined): URLParamsHelper {
    if (value) {
      this._query.set(param, value);
    }
    return this;
  }

  /**
   * Gets the value of a URL parameter.
   *
   * @param param - The name of the parameter to get the value of.
   * @return - Returns the value of the parameter or null if it doesn't exist.
   */
  get(param: string): string | null {
    return this._query.get(param) ?? null;
  }

  /**
   * Checks if a URL parameter exists in the URL.
   *
   * @param param - The name of the parameter to check.
   * @return - Returns true if the parameter exists, false otherwise.
   */
  has(param: string): boolean {
    return this._query.has(param);
  }

  /**
   * Renames a URL parameter to a new name.
   *
   * @param param - The name of the parameter to rename.
   * @param newParam - The new name for the parameter.
   * @return Returns the instance of URLParamsHelper for method chaining.
   */
  renameParam(param: string, newParam: string): URLParamsHelper {
    const oldValue = this._query.get(param);
    if (oldValue) {
      this._query.delete(param);
      this._query.set(newParam, oldValue);
    }
    return this;
  }

  /**
   * Adds all queries from the provided URLParamsHelper to the current instance.
   *
   * @param helper - The URLParamsHelper instance to add queries from.
   * @return The current instance for method chaining.
   */
  addQueries(helper: URLParamsHelper): URLParamsHelper {
    helper._query.forEach((value, key) => {
      this._query.set(key, value);
    });
    return this;
  }

  private isFullyEncoded(value: string): boolean {
    return (
      decodeURIComponent(value) !== value &&
      value === encodeURIComponent(decodeURIComponent(value))
    );
  }

  private queryToString(): string {
    if (this._query.size === 0) {
      return '';
    }
    const suffix = this._withSuffix ? this._sep : '';

    const tempDuplicatedKeyPrefix = new RegExp(`^${this._tempDuplicatedKeyPrefix}.*`);

    const queryAsString = [...this._query.entries()]
      .map(([key, value]) => {
        if (key.match(tempDuplicatedKeyPrefix)) {
          const { originalName, val } = JSON.parse(value);

          return `${originalName}=${val}`;
        }
        return `${key}=${value}`;
      })
      .join(this._sep);

    const prefix = this._parsable ? '?' : '';

    return `${prefix}${queryAsString}${suffix}`;
  }

  toString(): string {
    const url = this.__url + this.queryToString();
    return this._shouldBeEncoded ? encodeURIComponent(url) : url;
  }
}
