import { DaiAdsRequestCommon } from '../../interfaces';
import { URLParamsHelper } from './urlHelper';

export const createADORequestUrl = (
  baseConfigUrl: string,
  providerRequest: DaiAdsRequestCommon
): string => {
  const { uid, gdprConsent, gdpr, custParams, ip } = providerRequest;

  const helper = new URLParamsHelper(baseConfigUrl);

  if (custParams?.length) {
    const helper2 = new URLParamsHelper(custParams);

    if (helper2.has('ado_id')) {
      helper.delete('id');
      helper2.renameParam('ado_id', 'id');
    }

    if (helper2.has('uid') && uid) {
      helper2.delete('uid');
    }

    helper.addQueries(helper2);
  }
  helper.addMaybe('aouserid', uid).addMaybe('advid', ip);

  if (!gdpr || gdpr === '0' || !gdprConsent) {
    helper.delete('gdpr').delete('gdpr_consent');
  }

  return helper.toString();
};
