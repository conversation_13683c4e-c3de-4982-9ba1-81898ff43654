import { DaiAdsRequestCommon } from '../../interfaces';
import { URLParamsHelper } from './urlHelper';

export const createGAMRequestUrl = (
  baseConfigUrl: string,
  providerRequest: DaiAdsRequestCommon
): string => {
  const { gdpr, gdprConsent, uid, custParams } = providerRequest;

  const helper = new URLParamsHelper(baseConfigUrl, '&');

  if (helper.has('cust_params')) {
    const currentCustParams = helper.get('cust_params')!;
    const currentCustParamsHelper = new URLParamsHelper(currentCustParams);

    helper.delete('cust_params').addQueries(currentCustParamsHelper);
  }

  if (custParams) {
    const additionalParamsHelper = new URLParamsHelper(custParams);
    helper.addQueries(additionalParamsHelper);
  }

  if (gdpr === '1') {
    helper.delete('npa');
  }

  if (!gdpr || gdpr === '0' || !gdprConsent) {
    helper.delete('gdpr').delete('gdpr_consent');
  }

  helper.addMaybe('ppid', uid);

  return helper.toString();
};
