import { LogLevel } from 'adpod-tools';
import { FastifyRequest } from 'fastify';
import { v4 as uuid } from 'uuid';
import logger from '../../libs/logging/logger';
import { getApmIp } from './getIp';

/**
 * Interface for comprehensive IP source data
 */
export interface IpSources {
  queryParam: string;
  xDeviceIp: string;
  xForwardedFor: string;
  directConnection: string;
}

/**
 * Interface for comprehensive User Agent source data
 */
export interface UserAgentSources {
  queryParam: string;
  xDeviceUserAgent: string;
  userAgent: string;
}

/**
 * Interface for selected values that will be used by the application
 */
export interface SelectedValues {
  ip: string | undefined;
  userAgent: string | undefined;
}

/**
 * Interface for comprehensive request logging data
 */
export interface ComprehensiveRequestLogData {
  sessionId: string;
  timestamp: number;
  url: string;
  method: string;
  ipSources: IpSources;
  userAgentSources: UserAgentSources;
  selectedValues: SelectedValues;
}

/**
 * Safely extracts a string value from headers
 * @param value - Header value (can be string, string[], or undefined)
 * @returns String value or "empty" if not available
 */
function extractValue(value: string | string[] | undefined): string | undefined {
  return Array.isArray(value) ? value[0] : value;
}

const transformToString = (value: string | string[] | undefined): string => {
  return Array.isArray(value) ? value.join(',') : (value ?? '');
};

/**
 * Extracts all IP address sources from the request
 * @param req - Fastify request object
 * @returns Object containing all IP sources
 */
export function extractAllIpSources(req: FastifyRequest): IpSources {
  const query = req.query as { ip?: string | string[] };

  return {
    queryParam: transformToString(query?.ip) || 'empty',
    xDeviceIp: transformToString(req.headers['x-device-ip']) || 'empty',
    xForwardedFor: transformToString(req.headers['x-forwarded-for']) || 'empty',
    directConnection: req.socket?.remoteAddress || 'empty'
  };
}

/**
 * Extracts all User Agent sources from the request
 * @param req - Fastify request object
 * @returns Object containing all User Agent sources
 */
export function extractAllUserAgentSources(req: FastifyRequest): UserAgentSources {
  const query = req.query as { ua?: string | string[] };

  return {
    queryParam: transformToString(query?.ua),
    xDeviceUserAgent: transformToString(req.headers['x-device-user-agent']),
    userAgent: transformToString(req.headers['user-agent'])
  };
}

/**
 * Safely extracts User Agent using the same logic as getApmUserAgent but with proper type handling
 * @param req - Fastify request object
 * @param queryUserAgent - Optional User Agent from query parameters
 * @returns User Agent string or undefined
 */
function safeGetApmUserAgent(
  req: FastifyRequest,
  queryUserAgent?: string
): string | undefined {
  if (queryUserAgent) return queryUserAgent;

  const headers = req.raw.headers;

  return (
    transformToString(headers['x-device-user-agent']) ||
    transformToString(headers['user-agent'])
  );
}

/**
 * Gets the selected IP and User Agent values that will be used by the application
 * @param req - Fastify request object
 * @returns Object containing selected values
 */
export function getSelectedValues(req: FastifyRequest): SelectedValues {
  const query = req.query as { ip?: string | string[]; ua?: string | string[] };

  const queryIp = extractValue(query?.ip);
  const queryUa = extractValue(query?.ua);

  return {
    ip: getApmIp(req, queryIp) || 'empty',
    userAgent: safeGetApmUserAgent(req, queryUa) || 'empty'
  };
}

/**
 * Performs comprehensive logging of all IP and User Agent sources for a request
 * @param req - Fastify request object
 * @returns Generated request ID for correlation
 */
export function logComprehensiveRequestData(req: FastifyRequest): string {
  const sessionId = uuid();

  try {
    const ipSources = extractAllIpSources(req);
    const userAgentSources = extractAllUserAgentSources(req);
    const selectedValues = getSelectedValues(req);

    const logData: ComprehensiveRequestLogData = {
      sessionId,
      timestamp: Date.now(),
      url: req.raw.url || 'unknown',
      method: req.raw.method || 'unknown',
      ipSources,
      userAgentSources,
      selectedValues
    };

    logger('COMPREHENSIVE_REQUEST_LOGGING', { ...logData }, LogLevel.info);
  } catch (error) {
    logger(
      'ERROR_COMPREHENSIVE_REQUEST_LOGGING',
      {
        sessionId,
        error: error instanceof Error ? error.message : 'Unknown error',
        url: req.raw.url
      },
      LogLevel.error
    );
  }

  return sessionId;
}
