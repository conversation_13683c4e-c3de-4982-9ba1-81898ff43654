import { FastifyRequest } from 'fastify';
import is from './is';
import { IncomingHttpHeaders } from 'node:http';

/**
 * Headers that contain APM specific IP addresses
 */
const APM_HEADERS = ['x-device-ip'] as const;

/**
 * Headers that contain client IP addresses
 */
const CLIENT_HEADERS = ['x-client-ip'] as const;

/**
 * List of other possible IP headers in order of preference
 * These are checked as fallbacks when primary headers don't contain valid IPs
 */
const FALLBACK_IP_HEADERS = [
  'x-forwarded-for',
  'cf-connecting-ip',
  'fastly-client-ip',
  'true-client-ip',
  'x-real-ip',
  'x-cluster-client-ip',
  'x-forwarded',
  'forwarded-for',
  'forwarded',
  'x-appengine-user-ip',
  'Cf-Pseudo-IPv4'
] as const;

/**
 * Transforms IP value(s) to a comma-separated string.
 * If the input is already a string, it is returned as is.
 * @param ips - Single IP string or array of IP strings
 * @returns Comma-separated string of IPs
 */
const transformToString = (ips: string | string[]): string => {
  return Array.isArray(ips) ? ips.join(',') : ips;
};

/**
 * Extracts and validates the first valid IP address from a header value.
 * Handles comma-separated IPs and IPv4 addresses with ports.
 * @param headerValue - Header value that may contain IP address(es)
 * @returns First valid IP address found, or undefined if none found
 */
function findIp(headerValue: string | string[] | null | undefined): string | undefined {
  if (!headerValue || headerValue.length === 0) {
    return undefined;
  }

  const headerAsString = transformToString(headerValue);

  const ips = headerAsString.split(',').map((headerPart) => {
    const ip = headerPart.trim();

    if (ip.includes(':')) {
      const splitted = ip.split(':');

      if (splitted.length === 2) {
        return splitted[0];
      }
    }

    return ip;
  });

  return ips.find((ip) => is.ip(ip));
}

/**
 * Scans through a list of headers to find the first valid IP address.
 * @param headers - HTTP headers object
 * @param headersToScan - Array of header names to check
 * @returns First valid IP address found, or undefined if none found
 */
const scanHeaders = (
  headers: IncomingHttpHeaders,
  headersToScan: readonly string[]
): string | undefined => {
  for (const header of headersToScan) {
    const foundIp = findIp(headers[header]);

    if (foundIp) {
      return foundIp;
    }
  }
  return undefined;
};

/**
 * Attempts to extract IP address from fallback headers and socket information.
 * @param req - Fastify request object or raw request
 * @returns IP address from fallback sources, or undefined if none found
 */
const getIpFromOtherHeaders = (
  req: FastifyRequest | FastifyRequest['raw']
): string | undefined => {
  if (req.headers) {
    const foundIp = scanHeaders(req.headers, FALLBACK_IP_HEADERS);

    if (foundIp) {
      return foundIp;
    }
  }

  if (is.ip(req.socket?.remoteAddress)) {
    return req.socket.remoteAddress;
  }

  if ('raw' in req) {
    return getIpFromOtherHeaders(req.raw);
  }

  return undefined;
};

/**
 * Extracts client IP address from request headers
 * Checks client-specific headers first, then falls back to other common IP headers
 * @param req - Fastify request object or raw request
 * @returns Client IP address, or undefined if none found
 */
export function getClientIp(req: FastifyRequest | FastifyRequest['raw']): string | undefined {
  if (req.headers) {
    const foundIp = scanHeaders(req.headers, CLIENT_HEADERS);

    if (foundIp) {
      return foundIp;
    }
  }

  return getIpFromOtherHeaders(req);
}

/**
 * Extracts APM IP address from request
 * Prioritizes query parameter, then APM-specific headers, then falls back to other sources
 * @param req - Fastify request object
 * @param queryIp - Optional IP address from query parameters
 * @returns APM IP address, or undefined if none found
 */
export function getApmIp(req: FastifyRequest, queryIp?: string): string | undefined {
  if (queryIp && is.ip(queryIp)) {
    return queryIp;
  }

  const foundIp = scanHeaders(req.raw.headers, APM_HEADERS);

  if (foundIp) {
    return foundIp;
  }

  return getIpFromOtherHeaders(req);
}
