import { isEmptyObj } from './isEmptyObj';

describe('isEmptyObj test suite', () => {
  test('is a function', () => {
    expect(typeof isEmptyObj).toEqual('function');
  });

  test('is empty obj; true', () => {
    expect(isEmptyObj({})).toBe(true);
  });

  test('is empty obj; false', () => {
    expect(isEmptyObj({ bid: '1' })).toBe(false);
  });

  test('is empty obj; array', () => {
    expect(isEmptyObj([])).toBe(false);
  });

  test('is empty obj; string', () => {
    expect(isEmptyObj('')).toBe(false);
  });
});
