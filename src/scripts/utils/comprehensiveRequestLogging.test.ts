import { FastifyRequest } from 'fastify';
import { 
  extractAllIpSources, 
  extractAllUserAgentSources, 
  getSelectedValues,
  logComprehensiveRequestData
} from './comprehensiveRequestLogging';

// Mock logger to avoid actual logging during tests
jest.mock('../../libs/logging/logger', () => jest.fn());

// Mock uuid to have predictable request IDs
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'test-request-id-123')
}));

// Mock the getApmIp function
jest.mock('./getIp', () => ({
  getApmIp: jest.fn((req, queryIp) => {
    if (queryIp) return queryIp;
    if (req.raw.headers['x-device-ip']) return req.raw.headers['x-device-ip'];
    if (req.raw.headers['x-forwarded-for']) return req.raw.headers['x-forwarded-for'];
    return req.raw.socket?.remoteAddress;
  })
}));

describe('Comprehensive Request Logging', () => {
  const createMockRequest = (options: {
    query?: any;
    headers?: any;
    remoteAddress?: string;
    url?: string;
    method?: string;
  } = {}): FastifyRequest => ({
    query: options.query || {},
    raw: {
      headers: options.headers || {},
      socket: { remoteAddress: options.remoteAddress },
      url: options.url || '/test',
      method: options.method || 'GET'
    }
  } as FastifyRequest);

  describe('extractAllIpSources', () => {
    it('should extract all IP sources correctly', () => {
      const req = createMockRequest({
        query: { ip: '*******' },
        headers: {
          'x-device-ip': '*******',
          'x-forwarded-for': '*******'
        },
        remoteAddress: '*******'
      });

      const result = extractAllIpSources(req);

      expect(result).toEqual({
        queryParam: '*******',
        xDeviceIp: '*******',
        xForwardedFor: '*******',
        directConnection: '*******'
      });
    });

    it('should return "empty" for missing IP sources', () => {
      const req = createMockRequest();

      const result = extractAllIpSources(req);

      expect(result).toEqual({
        queryParam: 'empty',
        xDeviceIp: 'empty',
        xForwardedFor: 'empty',
        directConnection: 'empty'
      });
    });

    it('should handle array query parameters', () => {
      const req = createMockRequest({
        query: { ip: ['*******', '*******'] }
      });

      const result = extractAllIpSources(req);

      expect(result.queryParam).toBe('*******');
    });
  });

  describe('extractAllUserAgentSources', () => {
    it('should extract all User Agent sources correctly', () => {
      const req = createMockRequest({
        query: { ua: 'query-user-agent' },
        headers: {
          'x-device-user-agent': 'device-user-agent',
          'user-agent': 'standard-user-agent'
        }
      });

      const result = extractAllUserAgentSources(req);

      expect(result).toEqual({
        queryParam: 'query-user-agent',
        xDeviceUserAgent: 'device-user-agent',
        userAgent: 'standard-user-agent'
      });
    });

    it('should return "empty" for missing User Agent sources', () => {
      const req = createMockRequest();

      const result = extractAllUserAgentSources(req);

      expect(result).toEqual({
        queryParam: 'empty',
        xDeviceUserAgent: 'empty',
        userAgent: 'empty'
      });
    });

    it('should handle array headers correctly', () => {
      const req = createMockRequest({
        headers: {
          'x-device-user-agent': ['first-ua', 'second-ua'],
          'user-agent': ['first-standard-ua', 'second-standard-ua']
        }
      });

      const result = extractAllUserAgentSources(req);

      expect(result.xDeviceUserAgent).toBe('first-ua');
      expect(result.userAgent).toBe('first-standard-ua');
    });
  });

  describe('getSelectedValues', () => {
    it('should return selected IP and User Agent values', () => {
      const req = createMockRequest({
        query: { ip: '*******', ua: 'test-user-agent' },
        headers: {
          'x-device-ip': '*******',
          'x-device-user-agent': 'device-user-agent'
        }
      });

      const result = getSelectedValues(req);

      expect(result.ip).toBe('*******'); // Query param takes precedence
      expect(result.userAgent).toBe('test-user-agent'); // Query param takes precedence
    });

    it('should handle missing query parameters', () => {
      const req = createMockRequest({
        headers: {
          'x-device-ip': '*******',
          'x-device-user-agent': 'device-user-agent'
        }
      });

      const result = getSelectedValues(req);

      expect(result.ip).toBe('*******'); // Falls back to header
      expect(result.userAgent).toBe('device-user-agent'); // Falls back to header
    });
  });

  describe('logComprehensiveRequestData', () => {
    it('should return a request ID', () => {
      const req = createMockRequest();

      const requestId = logComprehensiveRequestData(req);

      expect(requestId).toBe('test-request-id-123');
    });

    it('should handle errors gracefully', () => {
      // Create a request that will cause an error
      const req = null as any;

      const requestId = logComprehensiveRequestData(req);

      expect(requestId).toBe('test-request-id-123');
    });
  });
});
