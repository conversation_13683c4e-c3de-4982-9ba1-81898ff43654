import { type FastifyRequest } from 'fastify';
import { getClientIp, getApmIp } from './getIp';
import { type IncomingHttpHeaders } from 'node:http';

describe('getClientIpRefactored', () => {
  const createMockRequest = (
    headers: IncomingHttpHeaders,
    socketRemoteAddress?: string
  ): FastifyRequest => {
    const socket = socketRemoteAddress ? { remoteAddress: socketRemoteAddress } : undefined;
    return {
      headers,
      socket,
      raw: {
        headers,
        socket
      }
    } as FastifyRequest;
  };

  describe('getClientIp', () => {
    it('should return IP from x-client-ip header', () => {
      const req = createMockRequest({ 'x-client-ip': '***********' });
      const result = getClientIp(req);

      expect(result).toBe('***********');
    });

    it('should return IP from x-forwarded-for header when client header is not present', () => {
      const req = createMockRequest({ 'x-forwarded-for': '********' });
      const result = getClientIp(req);

      expect(result).toBe('********');
    });

    it('should handle comma-separated IPs in x-forwarded-for', () => {
      const req = createMockRequest({
        'x-forwarded-for': '***********, ********, invalid-ip'
      });
      const result = getClientIp(req);

      expect(result).toBe('***********');
    });

    it('should handle IP with port', () => {
      const req = createMockRequest({ 'x-forwarded-for': '***********:8080' });
      const result = getClientIp(req);

      expect(result).toBe('***********');
    });

    it('should return IP from socket when headers are not available', () => {
      const req = createMockRequest({}, '*******');
      const result = getClientIp(req);

      expect(result).toBe('*******');
    });

    it('should return null when no valid IP is found', () => {
      const req = createMockRequest({ 'x-client-ip': 'invalid-ip' });
      const result = getClientIp(req);

      expect(result).toBeUndefined();
    });

    it('should handle array of header values', () => {
      const req = createMockRequest({ 'x-forwarded-for': ['***********', '********'] });
      const result = getClientIp(req);

      expect(result).toBe('***********');
    });

    it('should handle array of header values with port', () => {
      const req = createMockRequest({ 'x-forwarded-for': ['***********:8080', '********'] });
      const result = getClientIp(req);

      expect(result).toBe('***********');
    });

    it('should handle string with comma-separated list of header values', () => {
      const req = createMockRequest({ 'x-forwarded-for': '***********, ********' });
      const result = getClientIp(req);

      expect(result).toBe('***********');
    });

    it('should prioritize client headers over fallback headers', () => {
      const req = createMockRequest({
        'x-forwarded-for': '********',
        'x-client-ip': '***********'
      });
      const result = getClientIp(req);

      expect(result).toBe('***********');
    });

    it('should check multiple fallback headers in order', () => {
      const req = createMockRequest({
        'cf-connecting-ip': '***********',
        'fastly-client-ip': '********',
        'x-forwarded-for': 'invalid-ip'
      });
      const result = getClientIp(req);

      expect(result).toBe('***********');
    });
  });

  describe('getApmIp', () => {
    it('should return queryIp when provided and is valid', () => {
      const req = createMockRequest({ 'x-device-ip': '********' });
      const result = getApmIp(req, '***********');

      expect(result).toBe('***********');
    });

    it('should ignore invalid queryIp and check headers', () => {
      const req = createMockRequest({ 'x-device-ip': '***********' });
      const result = getApmIp(req, 'invalid-ip');

      expect(result).toBe('***********');
    });

    it('should return IP from x-device-ip header', () => {
      const req = createMockRequest({ 'x-device-ip': '***********' });
      const result = getApmIp(req);

      expect(result).toBe('***********');
    });

    it('should fall back to other headers when APM header is not present', () => {
      const req = createMockRequest({ 'x-forwarded-for': '********' });
      const result = getApmIp(req);

      expect(result).toBe('********');
    });

    it('should return null when no valid IP is found', () => {
      const req = createMockRequest({ 'x-device-ip': 'invalid-ip' });
      const result = getApmIp(req);

      expect(result).toBeUndefined();
    });

    it('should handle empty queryIp', () => {
      const req = createMockRequest({ 'x-device-ip': '***********' });
      const result = getApmIp(req, '');

      expect(result).toBe('***********');
    });
  });

  describe('edge cases', () => {
    it('should handle request without headers', () => {
      const req = { socket: { remoteAddress: '***********' } } as FastifyRequest;
      const result = getClientIp(req);

      expect(result).toBe('***********');
    });

    it('should handle empty header values', () => {
      const req = createMockRequest({ 'x-client-ip': '' });
      const result = getClientIp(req);

      expect(result).toBeUndefined();
    });

    it('should handle whitespace in header values', () => {
      const req = createMockRequest({ 'x-forwarded-for': '  ***********  ' });
      const result = getClientIp(req);

      expect(result).toBe('***********');
    });
  });
});
