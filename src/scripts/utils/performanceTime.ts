/**
 * Formats the performance time by subtracting the start time from the end time and returning the result as a string.
 *
 * @param {number} start - The start time in milliseconds.
 * @param {number} end - The end time in milliseconds.
 * @return {string} The formatted performance time as a string. If either the start time or the end time is not provided, it returns the string 'data not available (outdated node ver.)'.
 */
export const formatPerformanceTime = (start?: number, end?: number): number | string =>
  start && end ? end - start : 'data not avaliable (outdated node ver.)';

export const getCurrentPerformanceTime = (): number => Date.now();
