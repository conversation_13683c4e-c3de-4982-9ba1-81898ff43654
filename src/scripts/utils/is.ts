import { isIP } from 'net';

const existy = (value: unknown): value is NonNullable<unknown> =>
  value !== null && value !== undefined;
const isString = (value: unknown): value is string =>
  existy(value) && Object.prototype.toString.call(value) === '[object String]';
const isObject = (value: unknown): value is object =>
  Object.prototype.toString.call(value) === '[object Object]';
const isIp = (value: unknown): value is string => isString(value) && isIP(value) !== 0;

const is = {
  existy,
  ip: isIp,
  object: isObject,
  string: isString
};

export default is;
