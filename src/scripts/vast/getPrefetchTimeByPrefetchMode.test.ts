import { PrefetchType } from '../../models/prefetch.model';
import getPrefetchTimeByPrefetchMode from './getPrefetchTimeByPrefetchMode';

describe('getPrefetchTimeByPrefetchMode test suite', () => {
  test('is a function', () => {
    expect(typeof getPrefetchTimeByPrefetchMode).toEqual('function');
  });

  test('should return 24h', () => {
    const response24h = { minutes: 24 * 60, prefetchTime: '24:00:00' };

    expect(getPrefetchTimeByPrefetchMode(PrefetchType.hours24hMirrored, 4)).toEqual(
      response24h
    );
  });

  test('should return next worker config', () => {
    const next = 4;
    const responseNextH = { minutes: next * 60, prefetchTime: '04:00:00' };

    expect(getPrefetchTimeByPrefetchMode(PrefetchType.nextMirrored, next)).toEqual(
      responseNextH
    );
    expect(getPrefetchTimeByPrefetchMode(PrefetchType.next, next)).toEqual(responseNextH);
  });

  test('should return 0.5h', () => {
    const response30min = { minutes: 0.5 * 60, prefetchTime: '00:30:00' };

    expect(getPrefetchTimeByPrefetchMode(PrefetchType.minutes30, 4)).toEqual(response30min);
    expect(getPrefetchTimeByPrefetchMode(PrefetchType.minutes30Mirrored, 4)).toEqual(
      response30min
    );
  });

  test('should return 1h', () => {
    const response1h = { minutes: 1 * 60, prefetchTime: '01:00:00' };

    expect(getPrefetchTimeByPrefetchMode(PrefetchType.hours1, 4)).toEqual(response1h);
    expect(getPrefetchTimeByPrefetchMode(PrefetchType.hours1Mirrored, 4)).toEqual(response1h);
  });

  test('should return default value', () => {
    const defaultValue = { minutes: undefined, prefetchTime: undefined };

    expect(getPrefetchTimeByPrefetchMode('' as any, 4)).toEqual(defaultValue);
    expect(getPrefetchTimeByPrefetchMode(undefined as any, 4)).toEqual(defaultValue);
    expect(getPrefetchTimeByPrefetchMode(null as any, 4)).toEqual(defaultValue);
  });
});
