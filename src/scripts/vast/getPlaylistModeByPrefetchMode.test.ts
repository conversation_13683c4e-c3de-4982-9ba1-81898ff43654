import { PlaylistMode } from '../../models/playlistMode.model';
import { PrefetchType } from '../../models/prefetch.model';
import getPlaylistModeByPrefetchMode from './getPlaylistModeByPrefetchMode';

describe('getPlaylistModeByPrefetchMode test suite', () => {
  test('is a function', () => {
    expect(typeof getPlaylistModeByPrefetchMode).toEqual('function');
  });

  test('should return PlaylistMode=mirrored', () => {
    expect(getPlaylistModeByPrefetchMode(PrefetchType.nextMirrored)).toEqual(
      PlaylistMode.mirrored
    );
    expect(getPlaylistModeByPrefetchMode(PrefetchType.nextMirrored)).toEqual(
      PlaylistMode.mirrored
    );
    expect(getPlaylistModeByPrefetchMode(PrefetchType.minutes30Mirrored)).toEqual(
      PlaylistMode.mirrored
    );
    expect(getPlaylistModeByPrefetchMode(PrefetchType.hours1Mirrored)).toEqual(
      PlaylistMode.mirrored
    );
  });

  test('should return PlaylistMode=default', () => {
    expect(getPlaylistModeByPrefetchMode(PrefetchType.next)).toEqual(PlaylistMode.default);
    expect(getPlaylistModeByPrefetchMode(PrefetchType.minutes30)).toEqual(
      PlaylistMode.default
    );
    expect(getPlaylistModeByPrefetchMode(PrefetchType.hours1)).toEqual(PlaylistMode.default);
    expect(getPlaylistModeByPrefetchMode('foo' as any)).toEqual(PlaylistMode.default);
    expect(getPlaylistModeByPrefetchMode(undefined as any)).toEqual(PlaylistMode.default);
    expect(getPlaylistModeByPrefetchMode([] as any)).toEqual(PlaylistMode.default);
  });
});
