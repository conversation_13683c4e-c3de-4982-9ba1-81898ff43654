import { minutesPrefetchRegExp } from '../../components/playlist-multiple/validate/prefetchPlaylistQuery';
import { PlaylistMode } from '../../models/playlistMode.model';
import { PrefetchType } from '../../models/prefetch.model';

const getPlaylistModeByPrefetchMode = (mode: string): PlaylistMode => {
  if (minutesPrefetchRegExp.test(mode) && mode.includes('_debug')) {
    return PlaylistMode.debug;
  }

  switch (mode) {
    case PrefetchType.nextMirrored:
    case PrefetchType.minutes30Mirrored:
    case PrefetchType.hours1Mirrored:
    case PrefetchType.hours24hMirrored:
      return PlaylistMode.mirrored;
    case PrefetchType.nextDebug:
    case PrefetchType.nextDebugValid:
    case PrefetchType.minutes30Debug:
    case PrefetchType.hours1Debug:
    case PrefetchType.hours24hDebug:
    case PrefetchType.nextReplacedDebug:
      return PlaylistMode.debug;
    case PrefetchType.nextMixed:
    case PrefetchType.minutes30Mixed:
    case PrefetchType.hours1Mixed:
    case PrefetchType.hours24hMixed:
      return PlaylistMode.mixed;
    default:
      return PlaylistMode.default;
  }
};

export default getPlaylistModeByPrefetchMode;
