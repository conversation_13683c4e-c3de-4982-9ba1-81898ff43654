import { createEmptyVast } from './createEmptyVast';
import { PlaylistOutputs } from '../../models/playlistOutput.model';
import { Vast4 } from '../../models/vast4.model';

describe('createEmptyVast test suite', () => {
  test('is a function', () => {
    expect(typeof createEmptyVast).toEqual('function');
  });

  test('return string (xml)', async () => {
    expect(typeof (await createEmptyVast({} as any, ''))).toEqual('string');
  });

  test('return by default vast4 and vmap', async () => {
    expect(await createEmptyVast({} as any, '')).toEqual(Vast4.emptyVmap);
  });

  test('return by vast4 and vmap', async () => {
    expect(await createEmptyVast({} as any, '', PlaylistOutputs.default)).toEqual(
      Vast4.emptyVmap
    );
  });

  test('return empty vast 4.0', async () => {
    expect(await createEmptyVast({} as any, '', PlaylistOutputs.vast4)).toEqual(Vast4.empty);
  });

  test('return empty vast 4.0 and vmap (by default), invalid args', async () => {
    expect(await createEmptyVast({} as any, '', 'var23rt2')).toEqual(Vast4.emptyVmap);
  });
});
