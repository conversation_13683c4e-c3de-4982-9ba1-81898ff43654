import { isEmptyVast } from './isEmptyVast';
import { Vast4 } from '../../models/vast4.model';

describe('isEmptyVast test suite', () => {
  test('is a function', () => {
    expect(typeof isEmptyVast).toEqual('function');
  });

  test('return string (xml)', () => {
    expect(typeof isEmptyVast('')).toEqual('boolean');
  });

  test('empty string (xml)', () => {
    expect(isEmptyVast('')).toBe(true);
  });

  test('null (xml)', () => {
    expect(isEmptyVast(null)).toBe(true);
  });

  test('empty vast4', () => {
    expect(isEmptyVast(Vast4.empty)).toBe(true);
  });
});
