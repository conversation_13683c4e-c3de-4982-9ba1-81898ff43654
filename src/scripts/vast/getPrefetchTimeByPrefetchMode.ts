import { validators } from '../../EnvValidation/envalidConfig';
import { minutesPrefetchRegExp } from '../../components/playlist-multiple/validate/prefetchPlaylistQuery';
import { PrefetchType } from '../../models/prefetch.model';

export class PrefetchTime {
  constructor(
    public minutes: number | undefined,
    public prefetchTime: string | undefined
  ) {}
}

const nextReplacedOffset = validators.PREFETCH_NEXT_REPLACED_OFFSET;

const getPrefetchTimeByPrefetchMode = (mode: string, next: number): PrefetchTime => {
  if (minutesPrefetchRegExp.test(mode)) {
    const minutes = +mode.replace(/(m|_debug)/g, '');
    return new PrefetchTime(
      minutes,
      new Date(minutes * 60 * 1000).toISOString().substr(11, 8)
    );
  }

  switch (mode) {
    case PrefetchType.hours24hDebug:
    case PrefetchType.hours24hMirrored:
    case PrefetchType.hours24hMixed:
      return new PrefetchTime(24 * 60, '24:00:00');
    case PrefetchType.next:
    case PrefetchType.nextDebug:
    case PrefetchType.nextDebugValid:
    case PrefetchType.nextMirrored:
    case PrefetchType.nextMixed:
      return new PrefetchTime(next * 60, `${next.toString().padStart(2, '0')}:00:00`);
    case PrefetchType.hours1:
    case PrefetchType.hours1Debug:
    case PrefetchType.hours1Mirrored:
    case PrefetchType.hours1Mixed:
      return new PrefetchTime(1 * 60, '01:00:00');
    case PrefetchType.nextReplaced:
    case PrefetchType.nextReplacedDebug:
      return new PrefetchTime(
        nextReplacedOffset * 60,
        `${nextReplacedOffset < 10 ? '0' : ''}${nextReplacedOffset}:00:00`
      );
    case PrefetchType.minutes30:
    case PrefetchType.minutes30Debug:
    case PrefetchType.minutes30Mirrored:
    case PrefetchType.minutes30Mixed:
      return new PrefetchTime(0.5 * 60, '00:30:00');
    default:
      return new PrefetchTime(undefined, undefined);
  }
};

export default getPrefetchTimeByPrefetchMode;
