import { logger, LogLevel, prepareJsonVast4, xmlParser } from 'adpod-tools';
import { PlaylistOutputs } from '../../models/playlistOutput.model';
import { IRequestLog, Vmap } from '../../interfaces';

export const createEmptyVast = async (
  requestLog?: IRequestLog,
  emptyVastReason?: string,
  output: string = PlaylistOutputs.default
): Promise<string> => {
  if (requestLog && emptyVastReason) {
    logger('EMPTY_VAST_CREATED', { emptyVastReason, requestLog }, LogLevel.dev);
  }
  const vast4Json = prepareJsonVast4([]);

  const vmap: Vmap = {
    'vmap:VMAP': {
      _attributes: {
        'xmlns:vmap': 'http://iab.net/videosuite/vmap',
        version: '1.0'
      },
      'vmap:AdBreak': {
        _attributes: {
          timeOffset: 'HH:MM:SS',
          breakType: 'mirrored',
          breakId: 'empty'
        },
        'vmap:AdSource': {
          _attributes: {
            id: 'ads',
            allowMultipleAds: 'true',
            followRedirects: 'true'
          },
          'vmap:VASTAdData': vast4Json
        }
      }
    }
  };

  return xmlParser.fromJSONtoXML(output === PlaylistOutputs.vast4 ? vast4Json : vmap);
};
