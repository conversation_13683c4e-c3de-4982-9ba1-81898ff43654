import { AdVast4Normalized, Channel } from 'adpod-tools';
import { URLParamsHelper } from '../adserver/urlHelper';
import { Protocol } from '../../models/protocol.model';
import { TrackingType } from '../../interfaces';

export class AdSlotBuilder {
  private adSlotClone: AdVast4Normalized;

  constructor(private adSlot: AdVast4Normalized) {
    this.adSlotClone = structuredClone(this.adSlot);
  }

  addImpression(protocol: Protocol, data: Partial<AdTrackingData>) {
    this.adSlotClone.InLine?.Impression.push({
      _attributes: { id: '' },
      _cdata: this.createUrl(protocol, data)
    });

    return this;
  }

  addDefaultTrackingScripts(protocol: Protocol, data: Partial<AdTrackingData>) {
    const trackingScripts = [
      { event: 'start', cdata: this.createUrl(protocol, { ...data, e: '0' }) },
      { event: 'firstQuartile', cdata: this.createUrl(protocol, { ...data, e: '25' }) },
      { event: 'midpoint', cdata: this.createUrl(protocol, { ...data, e: '50' }) },
      { event: 'thirdQuartile', cdata: this.createUrl(protocol, { ...data, e: '75' }) },
      { event: 'complete', cdata: this.createUrl(protocol, { ...data, e: '100' }) }
    ];

    return this.addTrackingScripts(trackingScripts);
  }

  addTrackingScripts(scripts: Array<{ event: string; cdata: string }>) {
    this.adSlotClone.InLine?.Creatives.Creative.forEach((creative) => {
      creative.Linear?.TrackingEvents.Tracking.push(
        ...scripts.map(({ event, cdata }) => ({ _attributes: { event }, _cdata: cdata }))
      );
    });

    return this;
  }

  addBreakStartImpression(trackings: TrackingType[]) {
    const breakStart = trackings.find((t) => t._attributes.event === 'breakStart');
    if (!breakStart) {
      return this;
    }

    this.adSlotClone.InLine?.Impression.push({
      _attributes: { id: '' },
      _cdata: breakStart._cdata
    });

    return this;
  }

  addBreakEndTracking(trackings: TrackingType[]) {
    const breakStart = trackings.find((t) => t._attributes.event === 'breakEnd');
    if (!breakStart) {
      return this;
    }

    return this.addTrackingScripts([{ event: '', cdata: breakStart._cdata }]);
  }

  setUniversalAdIdForFirstCreative(value: string) {
    if (!this.adSlot.InLine) {
      return this;
    }

    this.adSlot.InLine.Creatives.Creative[0].UniversalAdId = {
      _text: value,
      _attributes: {}
    };

    return this;
  }

  build() {
    this.adSlot = this.adSlotClone;
  }

  private createUrl(protocol: Protocol, data: Partial<AdTrackingData>) {
    const entries = Object.entries(data);
    const urlHelper = new URLParamsHelper('', '/');

    entries.forEach(([key, value]) => urlHelper.addMaybe(key, value));

    return `${protocol}://dai-discoveryengage.tvn.pl/?ed=${urlHelper.toString()}`;
  }
}

type AdTrackingData = {
  p: number;
  bid: string;
  ch: Channel;
  uadid: string;
  v: string;
  uid: string;
  e: string;
  t: string;
  mode: string;
  dai_ad: string;
  tst_c: string;
  dur: number;
  c: string;
  bt: string;
  cust_params: string;
  m: string;
};
