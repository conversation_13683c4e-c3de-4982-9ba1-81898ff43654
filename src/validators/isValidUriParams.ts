import { URLSearchParams } from 'url';
import { validators } from '../EnvValidation/envalidConfig';

export const isValidUriParams = (
  value: string,
  helpers: { error: (arg0: string) => unknown }
): string | unknown => {
  const params = Object.fromEntries(new URLSearchParams(value));

  const excludes = validators.CUST_PARAMS_VALIDATION_EXCLUDES;
  const excludedParams = excludes.split(',');

  for (const excludedParam of excludedParams) {
    delete params[excludedParam];
  }

  for (const param in params) {
    if (!params[param]) {
      return helpers.error('any.invalid');
    }
  }

  return value;
};
