import { isValidUriParams } from './isValidUriParams';

describe('isValidUriParams test suite', () => {
  const helperMock = {
    error: () => false
  };

  test('is a function', () => {
    expect(typeof isValidUriParams).toBe('function');
  });

  test('validate, should return true, valid query params', () => {
    expect(isValidUriParams('customParamter=customValue', helperMock)).toEqual(
      'customParamter=customValue'
    );
  });

  test('validate, should return true, valid query params', () => {
    expect(
      isValidUriParams('customParamter=customValue&secondParam=secondValue', helperMock)
    ).toEqual('customParamter=customValue&secondParam=secondValue');
  });

  test('validate, should return false, invalid query params', () => {
    expect(isValidUriParams('customParamter=customValue&secondParam', helperMock)).toEqual(
      false
    );
  });

  test('validate, should return false, query param with empty value', () => {
    expect(
      isValidUriParams('customParamter=customValue&secondParam=&thirdParam=value3', helperMock)
    ).toEqual(false);
  });
});
