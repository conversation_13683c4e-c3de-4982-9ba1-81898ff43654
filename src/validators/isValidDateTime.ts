import { hasCorrectDateTimeFormat } from '../scripts/date/hasCorrectDateTimeFormat';

export const isValidDateTime = (
  value: string,
  helpers: { error: (arg0: string) => unknown }
): string | unknown => {
  const dateTime = value?.replace(' ', '+');

  return hasCorrectDateTimeFormat(dateTime) ? value : helpers.error('any.format');
};

export const isValidPlaylistSingleTime = (
  value: string,
  helpers: { error: (arg0: string) => unknown }
): string | unknown => {
  const dateTime = value?.replace(' ', '+');

  return hasCorrectDateTimeFormat(dateTime) || value === 'now'
    ? value
    : helpers.error('any.format');
};
