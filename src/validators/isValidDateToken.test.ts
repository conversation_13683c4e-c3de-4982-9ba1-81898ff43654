import dayjs from 'dayjs';
import { sha256 } from 'js-sha256';
import { isValidDateToken } from './isValidDateToken';

describe('isValidDateToken test suite', () => {
  const helperMock = {
    error: () => false
  };
  test('is a function', () => {
    expect(typeof isValidDateToken).toBe('function');
  });
  test('validate, should return true, valid bidDateToken', () => {
    const bidDateToken = sha256(dayjs().format('YYYYMM'));

    expect(isValidDateToken(bidDateToken, helperMock)).toEqual(bidDateToken);
  });

  test('validate, should return false, invalid bidDateToken', () => {
    const bidDateToken = sha256('20220101');

    expect(isValidDateToken(bidDateToken, helperMock)).toEqual(false);
  });

  test('validate, should return false, invalid sha256 hash', () => {
    const bidDateToken = 'testing';

    expect(isValidDateToken(bidDateToken, helperMock)).toEqual(false);
  });
});
