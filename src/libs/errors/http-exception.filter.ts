import { ExceptionFilter, Catch, ArgumentsHost, HttpException } from '@nestjs/common';
import { FastifyReply, FastifyRequest } from 'fastify';
import logger from '../logging/logger';
import { ResponseType } from '../../interfaces/response.interface';

function isResponseType(obj: unknown): obj is ResponseType {
  return (
    !!obj &&
    typeof obj === 'object' &&
    'statusCode' in obj &&
    'message' in obj &&
    'error' in obj
  );
}

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  async catch(exception: HttpException, host: ArgumentsHost): Promise<void> {
    const ctx = host.switchToHttp();
    const ctxResponse = ctx.getResponse<FastifyReply>();
    const { url: requestUrl } = ctx.getRequest<FastifyRequest>();

    const defaultResponse = exception.getResponse();
    const exceptionCode = exception.getStatus();

    let response = defaultResponse;

    const logData = { response, requestUrl, exceptionCode };

    logger('HTTP_EXCEPTION', { logData });

    if (isResponseType(defaultResponse)) {
      response = {
        ...defaultResponse,
        statusCode: exceptionCode,
        requestUrl
      };
    }

    await ctxResponse.status(exceptionCode).send(response);
  }
}
