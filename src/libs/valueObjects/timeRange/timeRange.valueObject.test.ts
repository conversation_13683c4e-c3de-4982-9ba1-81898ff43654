import dayjs from 'dayjs';
import { TimeRange } from './timeRange.valueObject';

describe('TimeRange value object', () => {
  test('should create a time range', () => {
    // arrange
    const start = dayjs();
    const end = dayjs().add(1, 'hour');

    // act
    const timeRange = TimeRange.create(start, end);

    // assert
    expect(timeRange).toBeDefined();
  });

  test('should return true if date is in time range', () => {
    // arrange
    const start = dayjs();
    const end = dayjs().add(1, 'hour');
    const timeRange = TimeRange.create(start, end);

    // act
    const result = timeRange.isDateInTimeRange(dayjs().add(30, 'minutes'));

    // assert
    expect(result).toBeTruthy();
  });

  test('should return false if date is outside time range', () => {
    // arrange
    const start = dayjs();
    const end = dayjs().add(1, 'hour');
    const timeRange = TimeRange.create(start, end);

    // act
    const result = timeRange.isDateInTimeRange(dayjs().add(2, 'hours'));

    // assert
    expect(result).toBeFalsy();
  });

  test('should return true if time range is contained in another time range', () => {
    // arrange
    const start = dayjs();
    const end = dayjs().add(1, 'hour');
    const timeRange = TimeRange.create(start, end);
    const otherTimeRange = TimeRange.create(
      dayjs().add(15, 'minutes'),
      dayjs().add(30, 'minutes')
    );

    // act
    const result = otherTimeRange.isContainedIn(timeRange);

    // assert
    expect(result).toBeTruthy();
  });

  test('should return false if time range is not contained in another time range', () => {
    // arrange
    const start = dayjs();
    const end = dayjs().add(1, 'hour');
    const timeRange = TimeRange.create(start, end);
    const otherTimeRange = TimeRange.create(
      dayjs().add(15, 'minutes'),
      dayjs().add(30, 'minutes')
    );

    // act
    const result = timeRange.isContainedIn(otherTimeRange);

    // assert
    expect(result).toBeFalsy();
  });

  test('should return duration in days', () => {
    // arrange
    const start = dayjs();
    const end = dayjs().add(5, 'days');
    const timeRange = TimeRange.create(start, end);

    // act
    const result = timeRange.getDuration('days');

    // assert
    expect(result).toEqual(5);
  });

  test('should return all dates in range', () => {
    // arrange
    const start = dayjs('2025-05-05T10:15:00Z').utc();
    const end = dayjs('2025-05-05T12:30:00Z').utc();
    const timeRange = TimeRange.create(start, end);

    // act
    const result = timeRange.getAllDatesInRange('hours');

    // assert
    expect(result).toEqual(['20250505_10:00:00', '20250505_11:00:00', '20250505_12:00:00']);
  });

  test('should return all dates in range when end date minutes are lower than start date minutes', () => {
    // arrange
    const start = dayjs('2025-07-08T04:50:41Z').utc();
    const end = dayjs('2025-07-08T05:20:41Z').utc();

    const timeRange = TimeRange.create(start, end);

    // act
    const result = timeRange.getAllDatesInRange('hours');

    // assert
    expect(result).toEqual(['20250708_04:00:00', '20250708_05:00:00']);
  });

  test('should return all dates in range from two days', () => {
    // arrange
    const start = dayjs('2025-05-05T23:15:00Z').utc();
    const end = dayjs('2025-05-06T01:30:00Z').utc();
    const timeRange = TimeRange.create(start, end);

    // act
    const result = timeRange.getAllDatesInRange('hours');

    // assert
    expect(result).toEqual(['20250505_23:00:00', '20250506_00:00:00', '20250506_01:00:00']);
  });

  test('should return true if time ranges overlap', () => {
    // arrange
    const start = dayjs('2025-05-05T10:00:00Z');
    const end = dayjs('2025-05-05T12:00:00Z');
    const timeRange = TimeRange.create(start, end);
    const otherTimeRange = TimeRange.create(
      dayjs('2025-05-05T08:00:00Z'),
      dayjs('2025-05-05T14:00:00Z')
    );

    // act
    const result = timeRange.isOverlapping(otherTimeRange);

    // assert
    expect(result).toBeTruthy();
  });

  test('should return false if time ranges not overlap', () => {
    // arrange
    const start = dayjs('2025-05-05T10:00:00Z');
    const end = dayjs('2025-05-05T12:00:00Z');
    const timeRange = TimeRange.create(start, end);
    const otherTimeRange = TimeRange.create(
      dayjs('2025-05-05T13:00:00Z'),
      dayjs('2025-05-05T14:00:00Z')
    );

    // act
    const result = timeRange.isOverlapping(otherTimeRange);

    // assert
    expect(result).toBeFalsy();
  });
});
