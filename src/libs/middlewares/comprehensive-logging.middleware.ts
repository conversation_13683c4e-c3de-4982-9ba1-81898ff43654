import { LogLevel } from 'adpod-tools';
import { FastifyReply, FastifyRequest } from 'fastify';
import { logComprehensiveRequestData } from '../../scripts/utils/comprehensiveRequestLogging';
import logger from '../logging/logger';
import { RequestContext, requestContextStorage } from '../request-context/request-context';

/**
 * Middleware that performs comprehensive logging of IP addresses and User Agents
 * for all incoming requests. This middleware:
 *
 * 1. Generates a unique request ID for correlation
 * 2. Logs all available IP sources (query param, headers, direct connection)
 * 3. Logs all available User Agent sources (query param, headers)
 * 4. Logs the selected values that will be used by the application
 * 5. Stores the request ID in the request context for correlation
 *
 * @param req - Fastify request object
 * @param res - Fastify reply object
 * @param next - Next function in the middleware chain
 */
export function comprehensiveLoggingMiddleware(
  req: FastifyRequest,
  _res: FastifyReply,
  next: () => void
) {
  try {
    const sessionId = logComprehensiveRequestData(req);

    const enhancedContext: RequestContext = {
      ...requestContextStorage.getStore(),
      sessionId
    };

    requestContextStorage.run(enhancedContext, () => next());
  } catch (error) {
    logger(
      'ERROR_COMPREHENSIVE_REQUEST_LOGGING',
      { error: error instanceof Error ? error.message : 'Unknown error' },
      LogLevel.error
    );
    next();
  }
}
