/* eslint-disable @typescript-eslint/no-explicit-any */
import { RequestContext, requestContextStorage } from '../request-context/request-context';
import { FastifyRequest, FastifyReply } from 'fastify';

export function requestContextMiddleware(
  req: FastifyRequest,
  res: FastifyReply,
  next: () => void
) {
  const query = req.query as any;

  const initialStore: RequestContext = { version: query?.v, channel: query?.ch };

  requestContextStorage.run(initialStore, () => next());
}
