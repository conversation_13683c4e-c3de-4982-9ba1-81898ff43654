import { Inject } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { CACHE_TOKEN } from './cache.constants';

export type CacheItem = {
  key: string;
  value: unknown;
  ttl?: number;
};

export abstract class ICacheProvider {
  abstract set(key: string, value: any, ttl?: number): Promise<void>;

  abstract setMany(items: CacheItem[]): Promise<void>;

  abstract get<T = any>(key: string): Promise<T | undefined>;

  abstract getMany<T = any>(keys: string[]): Promise<T[]>;

  abstract keys(): Promise<string[]>;

  abstract delete(key: string): Promise<void>;

  abstract deleteMany(keys: string[]): Promise<void>;

  abstract clear(): Promise<void>;

  abstract has(key: string): Promise<boolean>;
}

export class CacheProvider implements ICacheProvider {
  constructor(@Inject(CACHE_TOKEN) private readonly cacheProvider: Cache) {}

  async set(key: string, value: any, ttl?: number): Promise<void> {
    await this.cacheProvider.set(key, value, ttl);
  }

  async setMany(items: CacheItem[]): Promise<void> {
    await this.cacheProvider.mset(items);
  }

  async get<T = any>(key: string): Promise<T | undefined> {
    return this.cacheProvider.get<T>(key);
  }

  async getMany<T = any>(keys: string[]): Promise<T[]> {
    const result = await this.cacheProvider.mget<T>(keys);

    return result.filter((item) => item !== undefined);
  }

  async keys(): Promise<string[]> {
    const store = this.cacheProvider.stores[0];

    const result: string[] = [];
    if (!store?.iterator) {
      return [];
    }

    for await (const [key] of store.iterator({})) {
      result.push(key);
    }

    return result;
  }

  async delete(key: string): Promise<void> {
    await this.cacheProvider.del(key);
  }

  async deleteMany(keys: string[]): Promise<void> {
    await this.cacheProvider.mdel(keys);
  }

  async clear(): Promise<void> {
    await this.cacheProvider.clear();
  }

  async has(key: string): Promise<boolean> {
    const result = await this.cacheProvider.get(key);
    return !!result;
  }
}
