import { Module, Global } from '@nestjs/common';
import { CacheProvider, ICacheProvider } from './cache.provider';
import { createCache } from 'cache-manager';
import { validators } from '../../EnvValidation/envalidConfig';
import { CACHE_TOKEN } from './cache.constants';
import {
  DebugService,
  BreaksConfigurationCacheService,
  IDebugService,
  WorkerConfigCacheService
} from './services';

const days = validators.CACHE_SIZE_DAYS;
export const TTL = 1000 * 60 * 60 * 24 * days;

@Module({
  providers: [
    {
      provide: CACHE_TOKEN,
      useFactory: () =>
        createCache({
          ttl: TTL
        })
    },
    {
      provide: ICacheProvider,
      useClass: CacheProvider
    },
    {
      provide: IDebugService,
      useClass: DebugService
    },
    WorkerConfigCacheService,
    BreaksConfigurationCacheService
  ],
  exports: [
    ICacheProvider,
    IDebugService,
    BreaksConfigurationCacheService,
    WorkerConfigCacheService
  ]
})
@Global()
export class CacheModule {}
