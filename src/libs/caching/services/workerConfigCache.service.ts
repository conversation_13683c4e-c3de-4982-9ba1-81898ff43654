import { Injectable } from '@nestjs/common';
import { WorkerConfigType } from '../../../models/workerConfig';
import { ICacheProvider } from '../cache.provider';
import { validators } from '../../../EnvValidation/envalidConfig';
import { LogLevel } from 'adpod-tools';
import logger from '../../logging/logger';
import dayjs from 'dayjs';
import { TimeRange } from '../../valueObjects';
import { AWSS3FileContent } from 'adpod-aws';

@Injectable()
export class WorkerConfigCacheService {
  constructor(
    private readonly cacheManager: ICacheProvider,
    private readonly awsS3FileContent: AWSS3FileContent
  ) {}

  public async getWorkerConfig() {
    let workerConfig = await this.cacheManager.get<WorkerConfigType>('workerConfig');

    if (!workerConfig) {
      workerConfig = (await this.awsS3FileContent.getFileContent(
        validators.S3_WORKER_CONFIG_PATH
      )) as unknown as WorkerConfigType;
    }

    return workerConfig;
  }

  public async getScheduleConfigsAvailabilityTimeRange() {
    const { last, next } = await this.getScheduleConfigsAvailability();

    const now = dayjs();
    const lastDate = now.subtract(last, 'hour');
    const nextDate = now.add(next, 'hour');

    const timeRange = TimeRange.create(lastDate, nextDate);
    logger(
      'WORKER_CONFIG_SCHEDULE_CONFIGS_AVAILABILITY_TIME_RANGE',
      { scheduleConfigsAvailabilityTimeRange: timeRange.toFormat() },
      LogLevel.cache
    );

    return timeRange;
  }

  public async getScheduleConfigsAvailability() {
    const workerConfig = await this.getWorkerConfig();

    if (!workerConfig?.scheduleConfigsAvailability) {
      logger(
        'WORKER_CONFIG_SCHEDULE_CONFIGS_AVAILABILITY_NOT_FOUND',
        undefined,
        LogLevel.error
      );
      throw new Error('Worker config not found');
    }

    logger(
      'WORKER_CONFIG_SCHEDULE_CONFIGS_AVAILABILITY',
      { scheduleConfigsAvailability: workerConfig.scheduleConfigsAvailability },
      LogLevel.cache
    );

    return workerConfig.scheduleConfigsAvailability;
  }
}
