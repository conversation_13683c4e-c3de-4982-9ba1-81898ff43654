import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontex<PERSON>,
  CallHandler,
  HttpException
} from '@nestjs/common';
import dayjs from 'dayjs';
import { Observable, throwError, TimeoutError } from 'rxjs';
import { catchError, timeout } from 'rxjs/operators';
import { getActiveTraceId } from '../logging/trace';
import { validators } from '../../EnvValidation/envalidConfig';
import logger from '../logging/logger';
import { LogLevel } from 'adpod-tools';

class TimeoutException extends HttpException {
  constructor() {
    super(
      {
        message: `Timeout exception. Request elapsed time exceeded ${validators.REQUEST_TIMEOUT_MS} ms.`,
        dateTime: dayjs().format('YYYY-MM-DDTHH:mm:ss:SSSZ'),
        traceId: getActiveTraceId()
      },
      555
    );
  }
}

@Injectable()
export class TimeoutInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHand<PERSON>): Observable<any> {
    if (validators.REQUEST_TIMEOUT === 'ENABLED') {
      return next.handle().pipe(
        timeout(validators.REQUEST_TIMEOUT_MS),
        catchError((err) => {
          if (err instanceof TimeoutError) {
            logger(
              'ERROR_REQUEST_TIMEOUT_EXCEPTION',
              { err, limitMs: validators.REQUEST_TIMEOUT_MS },
              LogLevel.error
            );

            return throwError(() => new TimeoutException());
          }
          return throwError(() => err);
        })
      );
    }
    return next.handle();
  }
}
