/* eslint-disable @typescript-eslint/no-unused-vars */
import { ICacheProvider, CacheItem } from '../caching/cache.provider';

export class CacheProviderStub implements ICacheProvider {
  private readonly map: Map<string, any> = new Map();

  async set(key: string, value: any, ttl?: number): Promise<void> {
    this.map.set(key, value);
  }

  async setMany(items: CacheItem[]): Promise<void> {
    await Promise.all(items.map((item) => this.set(item.key, item.value, item.ttl)));
  }

  async get<T = any>(key: string): Promise<T | undefined> {
    return Promise.resolve(this.map.get(key));
  }

  async getMany<T = any>(keys: string[]): Promise<T[]> {
    const results = await Promise.all(keys.map((key) => this.map.get(key)));
    return Promise.resolve(results.filter((item) => item !== undefined));
  }

  async keys(): Promise<string[]> {
    return [...this.map.keys()];
  }

  async delete(key: string): Promise<void> {
    this.map.delete(key);
  }

  async deleteMany(keys: string[]): Promise<void> {
    await Promise.all(keys.map((key) => this.map.delete(key)));
  }

  async clear(): Promise<void> {
    this.map.clear();
  }

  async has(key: string): Promise<boolean> {
    return this.map.has(key);
  }
}
