{"id": "1", "duration": 245, "version": "v1_0_0", "channel": "TTV", "time": "2020-09-24T22:30:05+02:00", "adslot": [{"position": 1, "duration": 15, "type": "TV", "vastmirroredads": "vast1", "adrequest": "", "metadata": {"breakId": "1"}, "connector": "ADOCEAN_PROXY", "timeOffset": "2020-09-24T22:30:05+02:00"}, {"position": 2, "duration": 30, "type": "ATV", "vastmirroredads": "vast2", "adrequest": "https://tvn.adocean.pl/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid2dur=15/mid4dur=30", "metadata": {"breakId": "1"}, "connector": "ADOCEAN_PROXY", "timeOffset": "2020-09-24T22:30:05+02:00"}, {"position": 3, "duration": 15, "type": "TV", "vastmirroredads": "vast3", "adrequest": "", "metadata": {"breakId": "1"}, "connector": "ADOCEAN_PROXY", "timeOffset": "2020-09-24T22:30:05+02:00"}, {"position": 4, "duration": 30, "type": "ATV", "vastmirroredads": "vast4", "adrequest": "https://tvn.adocean.pl/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid4dur=30", "metadata": {"breakId": "1"}, "connector": "ADOCEAN_PROXY", "timeOffset": "2020-09-24T22:30:05+02:00"}, {"position": 5, "duration": 15, "type": "TV", "vastmirroredads": "vast5", "adrequest": "", "metadata": {"breakId": "1"}, "connector": "ADOCEAN_PROXY", "timeOffset": "2020-09-24T22:30:05+02:00"}, {"position": 1, "duration": 30, "type": "TV", "vastmirroredads": "vast1", "adrequest": "", "metadata": {"breakId": "2"}, "connector": "ADOCEAN_PROXY", "timeOffset": "2020-09-27T22:00:05+02:00"}, {"position": 2, "duration": 30, "type": "ATV", "vastmirroredads": "vast2", "adrequest": "https://tvn.adocean.pl/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid2dur=15/mid4dur=30", "metadata": {"breakId": "2"}, "connector": "ADOCEAN_PROXY", "timeOffset": "2020-09-27T22:00:05+02:00"}, {"position": 3, "duration": 20, "type": "TV", "vastmirroredads": "vast3", "adrequest": "", "metadata": {"breakId": "2"}, "connector": "ADOCEAN_PROXY", "timeOffset": "2020-09-27T22:00:05+02:00"}, {"position": 4, "duration": 30, "type": "ATV", "vastmirroredads": "vast4", "adrequest": "https://tvn.adocean.pl/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid4dur=30", "metadata": {"breakId": "2"}, "connector": "ADOCEAN_PROXY", "timeOffset": "2020-09-27T22:00:05+02:00"}, {"position": 5, "duration": 30, "type": "TV", "vastmirroredads": "vast5", "adrequest": "", "metadata": {"breakId": "2"}, "connector": "ADOCEAN_PROXY", "timeOffset": "2020-09-27T22:00:05+02:00"}]}