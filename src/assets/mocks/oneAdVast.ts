export const oneAdVast = {
  VAST: {
    _attributes: {
      version: '4.0',
      'xmlns:xs': 'http://www.w3.org/2001/XMLSchema',
      xmlns: 'http://www.iab.com/VAST'
    },
    Ad: [
      {
        _attributes: {
          id: 'Spot_2',
          campaignId: 'CA_37178,OR_4,CR_4',
          breakId: '7515464971096321',
          linear: 'true',
          sequence: 1,
          conditionalAd: false
        },
        InLine: {
          AdSystem: {
            _text: 'TVN',
            _attributes: { version: '4.0' }
          },
          AdTitle: {
            _text: 'TVN Video Ad'
          },
          Creatives: {
            Creative: [
              {
                Linear: {
                  Duration: {
                    _text: '00:00:15'
                  },
                  MediaFiles: {
                    MediaFile: [
                      {
                        _attributes: {
                          width: '720',
                          type: 'video/mp4',
                          height: '404',
                          delivery: 'progressive'
                        },
                        _cdata:
                          'https://r.dcs.redcdn.pl/http/o2/TVN-Adserver/y2016/v/d860edd1dd83b36f02ce52bde626c653/2905c856-ae04-4ad0-889d-9c84f1ff8218-www.mp4'
                      }
                    ]
                  },
                  VideoClicks: {
                    ClickThrough: {
                      _attributes: {
                        id: ''
                      }
                    }
                  },
                  TrackingEvents: {
                    Tracking: [
                      {
                        _attributes: {
                          event: 'start'
                        }
                      },
                      {
                        _attributes: {
                          event: 'start'
                        }
                      },
                      {
                        _attributes: {
                          event: 'firstQuartile'
                        }
                      },
                      {
                        _attributes: {
                          event: 'firstQuartile'
                        }
                      },
                      {
                        _attributes: {
                          event: 'midpoint'
                        }
                      },
                      {
                        _attributes: {
                          event: 'midpoint'
                        }
                      },
                      {
                        _attributes: {
                          event: 'thirdQuartile'
                        }
                      },
                      {
                        _attributes: {
                          event: 'thirdQuartile'
                        }
                      },
                      {
                        _attributes: {
                          event: 'complete'
                        }
                      },
                      {
                        _attributes: {
                          event: 'complete'
                        }
                      }
                    ]
                  }
                }
              },
              {
                CompanionAds: undefined
              }
            ]
          },
          Impression: [
            {
              _attributes: {
                id: ''
              }
            },
            {
              _attributes: {
                id: ''
              }
            },
            {
              _attributes: {
                id: ''
              }
            }
          ],
          Error: [],
          Extensions: {
            Extension: []
          }
        }
      }
    ]
  }
};
