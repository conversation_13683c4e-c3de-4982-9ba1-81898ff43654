import { AdType, BreakConnector, Channel, IConfiguration, OrderType } from 'adpod-tools';

export const testConfig: IConfiguration = {
  adslot: [
    {
      position: 1,
      duration: 30,
      type: AdType.tv,
      adrequest: '',
      metadata: {
        breakId: '764566994',
        orderType: OrderType.tv
      },
      connector: BreakConnector.adoceanSlotSchedule
    },
    {
      position: 2,
      duration: 30,
      type: AdType.tv,
      adrequest: '',
      metadata: {
        breakId: '764566994',
        orderType: OrderType.tv
      },
      connector: BreakConnector.adoceanSlotSchedule
    },
    {
      position: 3,
      duration: 15,
      type: AdType.tv,
      adrequest: '',
      metadata: {
        breakId: '764566994',
        orderType: OrderType.tv
      },
      connector: BreakConnector.adoceanSlotSchedule
    },
    {
      position: 4,
      duration: 20,
      type: AdType.tv,
      adrequest: '',
      metadata: {
        breakId: '764566994',
        orderType: OrderType.tv
      },
      connector: BreakConnector.adoceanSlotSchedule
    },
    {
      position: 5,
      duration: 15,
      type: AdType.tv,
      adrequest: '',
      metadata: {
        breakId: '764566994',
        orderType: OrderType.tv
      },
      connector: BreakConnector.adoceanSlotSchedule
    },
    {
      position: 6,
      duration: 30,
      type: AdType.tv,
      adrequest: '',
      metadata: {
        breakId: '764566994',
        orderType: OrderType.tv
      },
      connector: BreakConnector.adoceanSlotSchedule
    },
    {
      position: 7,
      duration: 30,
      type: AdType.tv,
      adrequest: '',
      metadata: {
        breakId: '764566994',
        orderType: OrderType.tv
      },
      connector: BreakConnector.adoceanSlotSchedule
    },
    {
      position: 8,
      duration: 10,
      type: AdType.tv,
      adrequest: '',
      metadata: {
        breakId: '764566994',
        orderType: OrderType.tv
      },
      connector: BreakConnector.adoceanSlotSchedule
    }
  ],
  time: '2021-02-11T02:26:43.000+01:00',
  duration: 180,
  channel: Channel.ttv,
  version: 'v1_0_0',
  id: '764566994'
};

export const testCaseData: IConfiguration[] = [testConfig];
