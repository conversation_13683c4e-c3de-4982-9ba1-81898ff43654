import { BreakConnector, Channel } from 'adpod-tools';

export const scenario1 = {
  id: '1',
  duration: 105,
  version: 'v1_0_0',
  channel: Channel.ttv,
  time: '2020-09-24T22:30:05+02:00',
  adslot: [
    {
      position: 1,
      duration: 15,
      type: 'TV',
      vastmirroredads: 'vast1',
      adrequest: '',
      connector: BreakConnector.adoceanSlotSchedule,
      metadata: {
        breakId: '123'
      }
    },
    {
      position: 2,
      duration: 30,
      type: 'ATV',
      vastmirroredads: 'vast2',
      adrequest:
        'https://tvn.adocean.pl/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid2dur=15/mid4dur=30',
      connector: BreakConnector.adoceanSlotSchedule,
      metadata: {
        breakId: '123'
      }
    },
    {
      position: 3,
      duration: 15,
      type: 'TV',
      vastmirroredads: 'vast3',
      adrequest: '',
      connector: BreakConnector.adoceanSlotSchedule,
      metadata: {
        breakId: '123'
      }
    },
    {
      position: 4,
      duration: 30,
      type: 'ATV',
      vastmirroredads: 'vast4',
      adrequest:
        'https://tvn.adocean.pl/ad.xml?aocodetype=1/id=efJbytQs_LETAKkcEgaqGZLxjE96bpC7nu.fyi7UHFr.97/mid4dur=30',
      connector: BreakConnector.adoceanSlotSchedule,
      metadata: {
        breakId: '123'
      }
    },
    {
      position: 5,
      duration: 15,
      type: 'TV',
      vastmirroredads: 'vast5',
      adrequest: '',
      connector: BreakConnector.adoceanSlotSchedule,
      metadata: {
        breakId: '123'
      }
    }
  ],
  metadata: { hasAtvSlot: true }
};
