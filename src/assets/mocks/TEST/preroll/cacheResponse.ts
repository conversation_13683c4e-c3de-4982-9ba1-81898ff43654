import { AdType, BreakConnector, Channel, IConfiguration, OrderType } from 'adpod-tools';

export const fullConfig: IConfiguration[] = [
  {
    id: '20240902MTIT000000434',
    time: '2024-09-03T00:11:02+02:00',
    duration: 80,
    channel: Channel.mtit,
    version: 'mock_v1_0_0_PREROLL',
    adslot: [
      {
        position: 1,
        duration: 20,
        type: AdType.tv,
        adrequest: '',
        metadata: {
          adId: 'ITA-90482',
          breakId: '20240902MTIT000000434',
          mezzanineUrl:
            'https://s3-tvn-owner-dicreatives.s3.eu-central-1.amazonaws.com/ITA-90482.mxf',
          orderType: OrderType.tv,
          atvType: { pending: [], processed: [] }
        },
        connector: BreakConnector.none
      },
      {
        position: 2,
        duration: 15,
        type: AdType.tv,
        adrequest: '',
        metadata: {
          adId: 'ITA-92758',
          breakId: '20240902MTIT000000434',
          mezzanineUrl:
            'https://s3-tvn-owner-dicreatives.s3.eu-central-1.amazonaws.com/ITA-92758.mxf',
          orderType: OrderType.tv,
          atvType: { pending: [], processed: [] }
        },
        connector: BreakConnector.none
      },
      {
        position: 3,
        duration: 15,
        type: AdType.tv,
        adrequest: '',
        metadata: {
          adId: 'ITA-93084',
          breakId: '20240902MTIT000000434',
          mezzanineUrl:
            'https://s3-tvn-owner-dicreatives.s3.eu-central-1.amazonaws.com/ITA-93084.mxf',
          orderType: OrderType.tv,
          atvType: { pending: [], processed: [] }
        },
        connector: BreakConnector.none
      },
      {
        position: 4,
        duration: 20,
        type: AdType.tv,
        adrequest: '',
        metadata: {
          adId: 'ITA-93155',
          breakId: '20240902MTIT000000434',
          mezzanineUrl:
            'https://s3-tvn-owner-dicreatives.s3.eu-central-1.amazonaws.com/ITA-93155.mxf',
          orderType: OrderType.tv,
          atvType: { pending: [], processed: [] }
        },
        connector: BreakConnector.none
      },
      {
        position: 5,
        duration: 10,
        type: AdType.tv,
        adrequest: '',
        metadata: {
          adId: 'ITA-93079',
          breakId: '20240902MTIT000000434',
          mezzanineUrl:
            'https://s3-tvn-owner-dicreatives.s3.eu-central-1.amazonaws.com/ITA-93079.mxf',
          orderType: OrderType.tv,
          atvType: { pending: [], processed: [] }
        },
        connector: BreakConnector.none
      }
    ],
    metadata: {
      deapProfiles: true,
      trackingDaiAds: true,
      exactLenght: false,
      diffCreatives: false,
      additionalTrackingsFW: true,
      preroll: true
    },
    prerollRequestUrl:
      '$HTTP_PROTOCOL://5e529.v.fwmrm.net/ad/g/1?prof=386345%3ADNI_IT_HbbTV_SSAI_live&nw=386345&metr=1031&caid=ADDRESSABLE_TV_DUMMY_ASSET&asnw=386345&csid=ADDRESSABLE_TV_SWITCH_IN_SPOT_ITALY_%24CHANNEL&vprn=$RAND_8&vip=$DEVICE_IP&vdur=3600&resp=vmap1%2Bvast4&flag=+scpv+emcr+amcb+slcb+aeti+sltp;_fw_vcid2=$UNIQUE_DEVICE_ID&_fw_h_user_agent=$DEVICE_USER_AGENT;ptgt=a&_fw_gdpr=$GDPR&_fw_gdpr_consent=$GDPR_CONSENT&mind=5&maxd=15&tpos=0&slau=Preroll%20Spot'
  }
];
