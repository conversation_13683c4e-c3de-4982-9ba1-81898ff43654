export enum PrefetchType {
  // next
  next = 'next',
  nextDebug = 'next_debug',
  nextDebugValid = 'next_debug_valid',
  nextMirrored = 'next_mirrored',
  nextMixed = 'next_mixed',
  // 30m
  minutes30 = '30m',
  minutes30Debug = '30m_debug',
  minutes30Mirrored = '30m_mirrored',
  minutes30Mixed = '30m_mixed',
  // 1h
  hours1 = '1h',
  hours1Debug = '1h_debug',
  hours1Mirrored = '1h_mirrored',
  hours1Mixed = '1h_mixed',
  // 2h
  nextReplaced = 'next_replaced',
  nextReplacedDebug = 'next_replaced_debug',
  // 24h
  hours24hDebug = '24h_debug',
  hours24hMirrored = '24h_mirrored',
  hours24hMixed = '24h_mixed'
}
