/**
 * The playlist mode.
 *
 * @readonly
 * @enum
 * @property mirrored - Mirrored ads only.
 * @property debug    - Mirrored + AdServer ads + extra `<Debug>` tag. If it cannot match AdServer ads, it returns mirrored ads.
 * @property mixed    - Mirrored + AdServer ads. If it cannot match AdServer ads, it returns mirrored ads.
 * @property preroll  - Preroll ads only.
 * @property preroll_debug - Preroll ads + AdServer ads + extra `<Debug>` tag. If it cannot match AdServer ads, it returns Preroll ads.
 * @property default  - Mirrored + AdServer ads. If cannot match AdServer ads, it returns empty VAST.
 */
export enum PlaylistMode {
  mirrored = 'mirrored',
  debug = 'debug',
  debugValid = 'debug_valid',
  mixed = 'mixed',
  preroll = 'preroll',
  preroll_debug = 'preroll_debug',
  default = ''
}

export enum DurationBasedPlaylistMode {
  debug = 'debug'
}
