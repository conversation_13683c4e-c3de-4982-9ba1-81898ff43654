import { availableItalyChannels, Channel } from 'adpod-tools';
import { LoggerSettingsType } from '../libs/logging/logger';

export type FreeWheelConfiguration = {
  filler: boolean;
};

export type FreeWheel = Record<string, FreeWheelConfiguration>;

export type ScheduleConfigsAvailability = {
  last: number;
  next: number;
  keepOutOfRangeConfigs: boolean;
  outOfRangeConfigsTTL: number;
};

export type SnowFlakeEnabledConfig = {
  channel: Channel | availableItalyChannels;
  version: string;
};

export type Snowflake = {
  ipLogging: boolean;
  snowFlakeEnabledConfigs: SnowFlakeEnabledConfig[];
};

export type WorkerConfigType = {
  loggingGroups: LoggerSettingsType;
  playlistSingleTimeThreshold: number;
  freeWheelDuration: FreeWheel;
  programmaticEnchancedVastPlversions: string[];
  snowflake: Snowflake;
  scheduleConfigsAvailability: ScheduleConfigsAvailability;
  versionLogging: string[];
};
