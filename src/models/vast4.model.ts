import { AdError, AdImpression, AdTracking } from 'adpod-tools';

export enum Vast4 {
  header = '<VAST version="4.0" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.iab.com/VAST">',
  empty = '<VAST version="4.0" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.iab.com/VAST"></VAST>',
  emptyVmap = '<vmap:VMAP xmlns:vmap="http://iab.net/videosuite/vmap" version="1.0"><vmap:AdBreak timeOffset="HH:MM:SS" breakType="mirrored" breakId="empty"><vmap:AdSource id="ads" allowMultipleAds="true" followRedirects="true"><vmap:VASTAdData><VAST version="4.0" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.iab.com/VAST"></VAST></vmap:VASTAdData></vmap:AdSource></vmap:AdBreak></vmap:VMAP>',
  dur15 = '<Duration>00:00:15</Duration>',
  dur30 = '<Duration>00:00:30</Duration>'
}

export type VastTrackings = {
  trackingEvents: AdTracking[];
  impressionEvents: AdImpression[];
  errorEvents: AdError[];
};
