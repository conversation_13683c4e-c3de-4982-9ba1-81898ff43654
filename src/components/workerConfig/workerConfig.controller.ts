import { Controller, Get, HttpException, HttpStatus } from '@nestjs/common';
import { Interval } from '@nestjs/schedule';
import { LogLevel } from 'adpod-tools';
import logger from '../../libs/logging/logger';
import { WorkerConfigService } from './workerConfig.service';
import { validators } from '../../EnvValidation/envalidConfig';

const updateInterval = validators.WORKER_CONFIG_INTERVAL_UPDATE_SECONDS;

@Controller('api/config')
export class WorkerConfigController {
  constructor(private readonly workerConfigService: WorkerConfigService) {}

  async onModuleInit(): Promise<void> {
    logger('WORKER_CONFIG_UPDATE_MODULE_INIT', {}, LogLevel.dev);

    await this.workerConfigService.update();
  }

  @Interval(updateInterval)
  @Get('/updateWorkerConfig')
  async updateConfig(): Promise<void> {
    try {
      await this.workerConfigService.update();
    } catch (e: unknown) {
      logger('ERROR_UNKNOWN_SERVER_ERROR', { e }, LogLevel.error);
      if (e instanceof HttpException) throw e;
      if (e instanceof Error) {
        throw new HttpException(e.message, HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }
  }
}
