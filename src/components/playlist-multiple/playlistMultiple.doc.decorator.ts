import { applyDecorators } from '@nestjs/common';
import { Api<PERSON><PERSON>ponse, ApiHeader, ApiProduces, ApiQuery } from '@nestjs/swagger';
import { PrefetchType } from '../../models/prefetch.model';
import { PlaylistOutputs } from '../../models/playlistOutput.model';

export function PrefetchDoc(): ReturnType<typeof applyDecorators> {
  return applyDecorators(
    ApiQuery({
      name: 'mode',
      required: true,
      description: `Prefetch Mode.
      \n\n*Default data* - mirrored + AdServer ads. If cannot match AdServer ads it returns empty VAST document.
      \n\n*Debug data* -  mirrored + AdServer ads + extra *&lt;Debug&gt;* tag. If it cannot match AdServer ads it returns mirrored ads.
      \n\n*Mirrored data* - mirrored ads only.
      \n\n*Mixed data* - mirrored + AdServer ads. If it cannot match AdServer ads it returns mirrored ads.
      \n\n*Replaced data* - mirrored + AdServer ads. It returns first break with replaced ads within 1h period.
      \n\n Possible values:
      \n  Playlist containing 1 ad block. Starts from: NOW + 1 min (default) or startDate or startBid.
      \n\n* *next* - Default data.
      \n\n* *next_debug* - Debug data.
      \n\n* *next_mirrored* - Mirrored data.
      \n\n* *next_mixed* - Mixed data.
      \n Playlist containing 1 ad block with replaced ads. Starts from: NOW + 1 min (default) or startDate or startBid. It searches the next hour period and returns first break with replaced ads.
      \n\n* *next_replaced* - Replaced data.
      \n\n* *next_replaced_debug* - Replaced data with debug info.
      \n  Playlist of ads blocks that start within 30 minutes. Starts from: NOW + 1 min (default) or startDate or startBid.
      \n\n* *30m* - Default data.
      \n\n* *30m_debug* - Debug data.
      \n\n* *30m_mirrored* - Mirrored data.
      \n\n* *30m_mixed* - Mixed data.
      \n  Playlist of ads blocks that start within 1 hour. Starts from: NOW + 1 min (default) or startDate or startBid.
      \n\n* *1h* - Default data.
      \n\n* *1h_debug* - Debug data.
      \n\n* *1h_mirrored* - Mirrored data.
      \n\n* *1h_mixed* - Mixed data.
      \n  Playlist of ads blocks that start within 24 hours. Starts from: NOW + 1 min (default) or startDate or startBid.
      \n\n* *24h_debug* - Debug data.
      \n\n* *24h_mirrored* - Mirrored data.
      \n\n* *24h_mixed* - Mixed data.
      \n  Playlist of ads blocks that start within X minutes, where "X" is a number between 1 and 1440. Starts from: NOW + 1 min (default) or startDate or startBid.
      \n\n* *Xm* - Default data.
      \n\n* *Xm_debug* - Debug data.`,
      type: 'enum',
      enum: [...Object.values(PrefetchType), 'Xm', 'Xm_debug'],
      enumName: 'PrefetchType'
    }),
    ApiQuery({
      name: 'ch',
      required: true,
      description: 'Broadcast channel id.',
      type: 'string'
    }),
    ApiQuery({
      name: 'v',
      required: false,
      description: 'Config version. Default value: *v1_0_0*',
      type: 'string'
    }),
    ApiQuery({
      name: 'uid',
      required: false,
      description: 'Unique user ID.',
      type: 'string'
    }),
    ApiQuery({
      name: 'startDate',
      required: false,
      description:
        'Playlist start date&time. If not provided - the playlist starts from the time the request is sent. Valid parameter format: YYYY-MM-DDTHH:MM:SS+XX:00',
      type: 'string'
    }),
    ApiQuery({
      name: 'startBid',
      required: false,
      description:
        'The playlist will start from the next break after the breakId sent as a value of startBid parameter',
      type: 'string'
    }),
    ApiQuery({
      name: 'output',
      required: false,
      description: 'Output format.',
      enum: PlaylistOutputs,
      enumName: 'PlaylistOutputsType',
      schema: { default: PlaylistOutputs.default }
    }),
    ApiQuery({
      name: 'cust_params',
      required: false,
      description:
        'Key-values pairs separated by *&*. If present the parameter will be used in adserver request.',
      type: 'string',
      example: 'paramOne=valueOne&paramTwo=valueTwo',
      schema: { default: '' }
    }),
    ApiQuery({
      name: 'npa',
      required: false,
      description: 'Non-personalised ads',
      enumName: 'NpaType',
      enum: ['0', '1']
    }),
    ApiQuery({
      name: 'duration',
      required: false,
      description:
        'Expected playlist duration in seconds. Available for mode=next* only. If playlist has different duration than the *duration* param value - empty vast will be returned.',
      type: 'number'
    }),
    ApiHeader({
      name: 'x-device-user-agent',
      required: false,
      description: 'Client’s device user agent.'
    }),
    ApiHeader({
      name: 'x-device-IP',
      required: false,
      description: 'Client’s device IP address.'
    }),
    ApiHeader({
      name: 'x-device-referrer',
      required: false,
      description: 'Client’s device referrer URL.'
    }),
    ApiHeader({
      name: 'x-tvn-links-response-proto',
      required: false,
      description:
        "AdServer and tracking URL's protocol. Request protocol is used if not provided."
    }),
    ApiProduces('application/xml'),
    ApiResponse({ status: 200, description: 'Valid response.' }),
    ApiResponse({ status: 304, description: 'Nothing was changed.' }),
    ApiResponse({ status: 400, description: 'Bad request.' }),
    ApiResponse({ status: 500, description: 'Server error.' })
  );
}
