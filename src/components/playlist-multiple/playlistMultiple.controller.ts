import { Controller, Get, HttpException, HttpStatus, Query, Req, Res } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { FastifyReply, FastifyRequest } from 'fastify';
import { LogLevel, returnAsArray } from 'adpod-tools';
import { v4 as uuid } from 'uuid';
import { PlaylistMultipleService } from './playlistMultiple.service';
import { isEmptyVast } from '../../scripts/vast/isEmptyVast';
import { PrefetchDoc } from './playlistMultiple.doc.decorator';
import { setPlaylistResHeaders } from '../../scripts/playlist/setPlaylistResHeaders';
import { RequestMacroParams } from '../../scripts/configuration/injectReqParams/requestMacroParams';
import { PrefetchPlaylistQuery } from './validate/prefetchPlaylistQuery';
import { Version } from '../../models/version.model';
import { sendResponseLogsToSnowflake } from '../../scripts/playlist/sendResponseLogsToSnowflake';
import { Protocol } from '../../models/protocol.model';
import { sendRequestLogsToSnowflake } from '../../scripts/playlist/sendRequestLogsToSnowflake';
import logger from '../../libs/logging/logger';
import {
  formatPerformanceTime,
  getCurrentPerformanceTime
} from '../../scripts/utils/performanceTime';
import { getActiveSpan, getActiveTraceId } from '../../libs/logging/trace';
import { getClientIp, getApmIp } from '../../scripts/utils/getIp';
import { getApmUserAgent } from '../../scripts/utils/getApmUserAgent';
import { WorkerConfigType } from '../../models/workerConfig';
import { ICacheProvider } from '../../libs/caching';

@Controller('api/playlist')
@ApiTags('playlist')
export class PlaylistMultipleController {
  constructor(
    private readonly playlistMultipleService: PlaylistMultipleService,
    private readonly localCache: ICacheProvider
  ) {}

  @Get(['/prefetch', 'schedule-based/prefetch'])
  @PrefetchDoc()
  async prefetch(
    @Req() req: FastifyRequest,
    @Query() queryParams: PrefetchPlaylistQuery,
    @Res() res: FastifyReply
  ): Promise<void> {
    try {
      const reqProcessingTimeStart = getCurrentPerformanceTime();

      const {
        output,
        mode,
        ch,
        v,
        uid,
        npa,
        startDateToken,
        startDate,
        cust_params,
        startBid,
        duration,
        gdpr,
        gdpr_consent,
        ip,
        ua
      } = queryParams;

      const span = getActiveSpan();

      const version: string = v || Version.base;
      const requestProtocol = (req.raw.headers['x-tvn-links-response-proto'] ||
        req.raw.headers['x-forwarded-proto'] ||
        req.protocol) as Protocol;

      const { ipLogging, snowFlakeExcludedConfigs } = await this.getRequestLoggingSettings();

      const userAgent = getApmUserAgent(req, ua);
      const ampIp = getApmIp(req, ip);

      const sessionId = uuid();
      const reqIP = getClientIp(req);
      const requestUrl = req.raw?.url;

      const requestDetails = {
        ...queryParams,
        rawHeaders: req.raw.headers,
        requestUrl,
        reqIP
      };

      logger('REQUEST_DETAILS', requestDetails, LogLevel.dev);

      if (span !== null) {
        span.setTag('tag-headers', req.headers);
        span.setTag('tag-rawHeaders', req.raw.headers);
        span.setTag('tag-requestUrl', requestUrl);
        span.setTag('tag-reqIP', reqIP);
        span.setTag('tag-version', v);
        span.setTag('tag-traceId', getActiveTraceId());
      }

      const requestIPforSnowflake = ipLogging ? ampIp : '';

      sendRequestLogsToSnowflake(
        sessionId,
        v!,
        ch,
        snowFlakeExcludedConfigs,
        requestUrl,
        uid,
        requestIPforSnowflake
      );

      const { playlist, emptyVastReason, playlistInfo, requestLog } =
        await this.playlistMultipleService.createMultipleBreaksVastPlaylist(
          mode,
          version,
          new RequestMacroParams(
            uid,
            requestProtocol,
            npa,
            cust_params,
            ch,
            gdpr,
            gdpr_consent
          ),
          !!startDateToken,
          startDate,
          cust_params,
          startBid,
          req.headers,
          output,
          ch,
          duration,
          ampIp,
          userAgent
        );

      logger(
        'PREFETCH_PLAYLIST_STATS',
        {
          ...sendResponseLogsToSnowflake(
            requestLog,
            res.elapsedTime,
            ampIp,
            req.headers,
            emptyVastReason,
            playlistInfo,
            snowFlakeExcludedConfigs,
            v,
            sessionId,
            ipLogging
          )
        },
        LogLevel.dev
      );

      setPlaylistResHeaders(
        res,
        isEmptyVast(playlist),
        req.raw.headers['x-tvn-links-response-proto'],
        req.raw.headers['x-forwarded-proto'],
        requestProtocol
      );

      const playlistShortStats = returnAsArray(playlistInfo).map((el) =>
        el
          ? {
              isWithReplacedAds: el.isWithReplacedAds,
              bid: el.bid,
              breakAllAdsCount: el.breakAllAdsCount,
              breakDaiPlaylistAdsCount: el.breakDaiPlaylistAdsCount,
              connector: el.connector
            }
          : el
      );

      const processingTime = +formatPerformanceTime(
        reqProcessingTimeStart,
        getCurrentPerformanceTime()
      );

      const reqProcessingTimeRounded = Math.round(processingTime / 10);

      logger(
        `STATS_PREFETCH_REQ_TIME_PTR_${reqProcessingTimeRounded}_V_${v}_CHANNEL_${ch}`,
        {
          processingTime,
          requestDetails,
          playlistShortStats,
          emptyVastReason
        },
        LogLevel.statsPrefetch
      );

      res.send(playlist);
    } catch (e: unknown) {
      logger('ERROR_UNKNOWN_SERVER_ERROR', { e }, LogLevel.error);
      if (e instanceof HttpException) throw e;
      if (e instanceof Error) {
        throw new HttpException({ ...e }, HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }
  }

  private async getRequestLoggingSettings() {
    const workerConfig = await this.localCache.get<WorkerConfigType>('workerConfig');

    return {
      ipLogging: workerConfig?.snowflake?.ipLogging ?? false,
      snowFlakeExcludedConfigs: workerConfig?.snowflake?.snowFlakeEnabledConfigs ?? []
    };
  }
}
