import { IConfiguration } from 'adpod-tools';
import { FillerAdsService, IFillerAdsService } from 'adpod-aws';
import { IVastJsonOperationalData } from '../../interfaces';
import { PlaylistOutputs } from '../../models/playlistOutput.model';
import { PrefetchType } from '../../models/prefetch.model';
import { PlaylistMultipleService } from './playlistMultiple.service';
import { RequestMacroParams } from '../../scripts/configuration/injectReqParams/requestMacroParams';
import { scenario1 } from '../../assets/mocks/simpleScenario1';
import { multipleAdsVast } from '../../assets/mocks/multipleAdsVast';
import { Protocol } from '../../models/protocol.model';
import { Test, TestingModule } from '@nestjs/testing';
import { JsonPlaylistService } from '../../scripts/services/createJsonPlaylist.service';
import { CustomParamsGenerator } from '../../scripts/services/customParamsGenerator.service';
import { AdOceanBreakDaiAdsProvider } from '../../scripts/services/daiAdsProvider/AdOcean/AdOceanBreakDaiAdsProvider';
import { AdOceanHandler } from '../../scripts/services/daiAdsProvider/AdOcean/AdOceanHandler.service';
import { AdOceanProxyDaiAdsProvider } from '../../scripts/services/daiAdsProvider/AdOcean/AdOceanProxyDaiAdsProvider';
import { UltimateDaiAdsProvider } from '../../scripts/services/daiAdsProvider/UltimateDaiAdsProvider.service';
import { FreeWheelDaiAdsProvider } from '../../scripts/services/daiAdsProvider/FreeWheel/freeWheelDaiAdsProvider.service';
import { GoogleAdManagerProvider } from '../../scripts/services/daiAdsProvider/GoogleAdsManager/googleAdManagerDaiAdsProvider.service';
import { DaiAdsProviderFactory } from '../../scripts/services/daiAdsProvider/daiAdsProviderFactory';
import dayjs from 'dayjs';
import {
  IDeapProfilesService,
  DeapProfileService
} from '../../scripts/services/deapProfiles.service';
import { TcfService } from '../../scripts/services/tcf.service';
import {
  FreeWheelFillersAdsProviderService,
  IFreeWheelFillersAdsProviderService
} from '../../scripts/services/daiAdsProvider/FreeWheel/freeWheelFillersAdsProvider.service';
import { IDebugService } from '../../libs/caching/services/debug.service';
import { createMock } from '@golevelup/ts-jest';
import { CacheProviderMock, TestCacheModule } from '../../libs/testing';
import { ICacheProvider, WorkerConfigCacheService } from '../../libs/caching';
import { IConfigurationService } from './services/configuration.service';
import { TestAwsModule, TestRedisModule } from 'adpod-aws/dist/testing';
import { PlaylistMerger } from '../../scripts/services/playlist/playlistMerger.service';
import { GeneralPlaylistTransformer } from '../../scripts/services/playlist/generalPlaylistTransformer.service';

const emptyVast4 =
  '<vmap:VMAP xmlns:vmap="http://iab.net/videosuite/vmap" version="1.0"><vmap:AdBreak timeOffset="HH:MM:SS" breakType="mirrored" breakId="empty"><vmap:AdSource id="ads" allowMultipleAds="true" followRedirects="true"><vmap:VASTAdData><VAST version="4.0" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.iab.com/VAST"></VAST></vmap:VASTAdData></vmap:AdSource></vmap:AdBreak></vmap:VMAP>';
const vmapVast4 =
  '<vmap:VMAP xmlns:vmap="http://iab.net/videosuite/vmap" version="1.0" prefetch="00:30:00"><vmap:AdBreak timeOffset="2020-09-24T22:30:05+02:00" breakType="mirrored" breakId="1"><vmap:AdSource id="ads" allowMultipleAds="true" followRedirects="true"><vmap:VASTAdData><VAST version="4.0" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.iab.com/VAST"><Ad id="Spot_2" campaignId="CA_37178,OR_4,CR_4" breakId="7515464971096321" linear="true" sequence="1" conditionalAd="false"><InLine><AdSystem version="4.0">TVN</AdSystem><AdTitle>TVN Video Ad</AdTitle><Creatives><Creative><Linear><Duration>00:00:15</Duration><MediaFiles><MediaFile width="720" type="video/mp4" height="404" delivery="progressive"><![CDATA[https://r.dcs.redcdn.pl/http/o2/TVN-Adserver/y2016/v/d860edd1dd83b36f02ce52bde626c653/2905c856-ae04-4ad0-889d-9c84f1ff8218-www.mp4]]></MediaFile></MediaFiles><VideoClicks><ClickThrough id=""/></VideoClicks><TrackingEvents><Tracking event="start"/><Tracking event="start"/><Tracking event="firstQuartile"/><Tracking event="firstQuartile"/><Tracking event="midpoint"/><Tracking event="midpoint"/><Tracking event="thirdQuartile"/><Tracking event="thirdQuartile"/><Tracking event="complete"/><Tracking event="complete"/></TrackingEvents></Linear></Creative><Creative/></Creatives><Impression id=""/><Impression id=""/><Impression id=""/><Extensions></Extensions></InLine></Ad><Ad id="Spot_4" campaignId="CA_37178,OR_5,CR_8" breakId="7515464971096321" linear="true" sequence="1" conditionalAd="false"><InLine><AdSystem version="4.0">TVN</AdSystem><AdTitle>TVN Video Ad</AdTitle><Creatives><Creative><Linear><Duration>00:00:30</Duration><MediaFiles><MediaFile width="720" type="video/mp4" height="404" delivery="progressive"><![CDATA[https://r.dcs.redcdn.pl/http/o2/TVN-Adserver/y2016/v/9f62b8625f914a002496335037e9ad97/d43ca65a-7d4f-46e5-bfbb-3e08a5858f0a-www.mp4]]></MediaFile></MediaFiles><VideoClicks><ClickThrough id=""/></VideoClicks><TrackingEvents><Tracking event="start"/><Tracking event="firstQuartile"/><Tracking event="midpoint"/><Tracking event="thirdQuartile"/><Tracking event="complete"/></TrackingEvents></Linear></Creative><Creative/></Creatives><Impression id=""/><Extensions></Extensions></InLine></Ad></VAST></vmap:VASTAdData></vmap:AdSource></vmap:AdBreak></vmap:VMAP>';
const vast4 =
  '<VAST version="4.0" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.iab.com/VAST" prefetch="00:30:00"><Ad id="Spot_2" campaignId="CA_37178,OR_4,CR_4" breakId="7515464971096321" linear="true" sequence="1" conditionalAd="false"><InLine><AdSystem version="4.0">TVN</AdSystem><AdTitle>TVN Video Ad</AdTitle><Creatives><Creative><Linear><Duration>00:00:15</Duration><MediaFiles><MediaFile width="720" type="video/mp4" height="404" delivery="progressive"><![CDATA[https://r.dcs.redcdn.pl/http/o2/TVN-Adserver/y2016/v/d860edd1dd83b36f02ce52bde626c653/2905c856-ae04-4ad0-889d-9c84f1ff8218-www.mp4]]></MediaFile></MediaFiles><VideoClicks><ClickThrough id=""/></VideoClicks><TrackingEvents><Tracking event="start"/><Tracking event="start"/><Tracking event="firstQuartile"/><Tracking event="firstQuartile"/><Tracking event="midpoint"/><Tracking event="midpoint"/><Tracking event="thirdQuartile"/><Tracking event="thirdQuartile"/><Tracking event="complete"/><Tracking event="complete"/></TrackingEvents></Linear></Creative><Creative/></Creatives><Impression id=""/><Impression id=""/><Impression id=""/><Extensions></Extensions></InLine></Ad><Ad id="Spot_4" campaignId="CA_37178,OR_5,CR_8" breakId="7515464971096321" linear="true" sequence="1" conditionalAd="false"><InLine><AdSystem version="4.0">TVN</AdSystem><AdTitle>TVN Video Ad</AdTitle><Creatives><Creative><Linear><Duration>00:00:30</Duration><MediaFiles><MediaFile width="720" type="video/mp4" height="404" delivery="progressive"><![CDATA[https://r.dcs.redcdn.pl/http/o2/TVN-Adserver/y2016/v/9f62b8625f914a002496335037e9ad97/d43ca65a-7d4f-46e5-bfbb-3e08a5858f0a-www.mp4]]></MediaFile></MediaFiles><VideoClicks><ClickThrough id=""/></VideoClicks><TrackingEvents><Tracking event="start"/><Tracking event="firstQuartile"/><Tracking event="midpoint"/><Tracking event="thirdQuartile"/><Tracking event="complete"/></TrackingEvents></Linear></Creative><Creative/></Creatives><Impression id=""/><Extensions></Extensions></InLine></Ad></VAST>';

describe('PlaylistMultipleService test suite', () => {
  const spyJson = jest.fn();

  let debugServiceMock: jest.Mocked<IDebugService>;
  let configurationServiceMock: jest.Mocked<IConfigurationService>;
  let cacheProviderMock: CacheProviderMock;

  const requestMacroParams = new RequestMacroParams('123', Protocol.http, '1');
  let playlistMultipleService: PlaylistMultipleService;

  beforeEach(async () => {
    jest.resetAllMocks();
    jest.clearAllMocks();

    debugServiceMock = createMock();
    configurationServiceMock = createMock();

    const app: TestingModule = await Test.createTestingModule({
      imports: [TestCacheModule, TestAwsModule, TestRedisModule],
      providers: [
        PlaylistMultipleService,
        CustomParamsGenerator,
        UltimateDaiAdsProvider,
        AdOceanHandler,
        GoogleAdManagerProvider,
        FreeWheelDaiAdsProvider,
        AdOceanBreakDaiAdsProvider,
        AdOceanProxyDaiAdsProvider,
        DaiAdsProviderFactory,
        PlaylistMerger,
        TcfService,
        WorkerConfigCacheService,
        GeneralPlaylistTransformer,
        {
          provide: IFreeWheelFillersAdsProviderService,
          useClass: FreeWheelFillersAdsProviderService
        },
        {
          provide: IFillerAdsService,
          useClass: FillerAdsService
        },
        {
          provide: IDeapProfilesService,
          useClass: DeapProfileService
        },
        {
          provide: IDebugService,
          useValue: debugServiceMock
        },
        {
          provide: JsonPlaylistService,
          useValue: {
            create: spyJson
          }
        },
        {
          provide: IConfigurationService,
          useValue: configurationServiceMock
        }
      ]
    }).compile();

    cacheProviderMock = app.get(ICacheProvider);
    playlistMultipleService = app.get(PlaylistMultipleService);

    cacheProviderMock.get.mockResolvedValueOnce({
      scheduleConfigsAvailability: {
        last: 1,
        next: 2,
        keepOutOfRangeConfigs: false,
        outOfRangeConfigsTTL: 4
      }
    });
  });

  test('playlistMultipleService is defined', () => {
    expect(playlistMultipleService).toBeDefined();
  });

  test('createMultipleBreaksVastPlaylist is a function', () => {
    expect(typeof playlistMultipleService.createMultipleBreaksVastPlaylist).toBe('function');
  });

  test('should return an empty vast when date is out of range', async () => {
    const result = await playlistMultipleService.createMultipleBreaksVastPlaylist(
      PrefetchType.minutes30Mirrored,
      'v1_0_0',
      requestMacroParams,
      false,
      '01-01-1970',
      undefined,
      undefined,
      {},
      undefined,
      undefined
    );
    expect(result.playlist).toEqual(emptyVast4);
  });

  test('should return an empty vast because there is no valid configurations', async () => {
    const result = await playlistMultipleService.createMultipleBreaksVastPlaylist(
      PrefetchType.minutes30Mirrored,
      'v1_0_0',
      requestMacroParams,
      false,
      '01-01-1970',
      undefined,
      undefined,
      {},
      undefined,
      undefined
    );
    expect(result.playlist).toEqual(emptyVast4);
  });

  test('should return an empty vast because there is no valid configurations after sort', async () => {
    cacheProviderMock.get.mockResolvedValueOnce([scenario1]);
    const result = await playlistMultipleService.createMultipleBreaksVastPlaylist(
      PrefetchType.minutes30Mirrored,
      'v1_0_0',
      requestMacroParams,
      false,
      '01-01-1970',
      undefined,
      undefined,
      {},
      undefined,
      undefined
    );
    expect(result.playlist).toEqual(emptyVast4);
  });

  test('should return non-empty vmap vast', async () => {
    configurationServiceMock.getConfigurations.mockResolvedValueOnce([
      scenario1 as unknown as IConfiguration
    ]);
    spyJson.mockReturnValueOnce({
      isWithReplacedAds: true,
      vast4Json: multipleAdsVast,
      adServerResponseLog: []
    } as IVastJsonOperationalData);
    const result = await playlistMultipleService.createMultipleBreaksVastPlaylist(
      PrefetchType.minutes30Mirrored,
      'v1_0_0',
      requestMacroParams,
      false,
      dayjs().add(6, 'hours').format('MM-DD-YYYY-HH:mm:ss'),
      undefined,
      undefined,
      {},
      undefined,
      undefined
    );
    expect(result.playlist).toEqual(vmapVast4);
  });

  test('should return non-empty vast4', async () => {
    configurationServiceMock.getConfigurations.mockResolvedValueOnce([
      scenario1 as unknown as IConfiguration
    ]);
    spyJson.mockReturnValueOnce(
      Promise.resolve({
        isWithReplacedAds: true,
        vast4Json: multipleAdsVast,
        adServerResponseLog: []
      } as IVastJsonOperationalData)
    );
    const result = await playlistMultipleService.createMultipleBreaksVastPlaylist(
      PrefetchType.minutes30Mirrored,
      'v1_0_0',
      requestMacroParams,
      false,
      dayjs().add(6, 'hours').format('MM-DD-YYYY-HH:mm:ss'),
      undefined,
      undefined,
      {},
      PlaylistOutputs.vast4,
      undefined
    );
    expect(result.playlist).toEqual(vast4);
  });

  test('should return non-empty vast when date out of range is allowed', async () => {
    configurationServiceMock.getConfigurations.mockResolvedValueOnce([
      scenario1 as unknown as IConfiguration
    ]);
    spyJson.mockReturnValueOnce({
      isWithReplacedAds: true,
      vast4Json: multipleAdsVast,
      adServerResponseLog: []
    } as IVastJsonOperationalData);
    const result = await playlistMultipleService.createMultipleBreaksVastPlaylist(
      PrefetchType.minutes30Mirrored,
      'v1_0_0',
      requestMacroParams,
      true,
      dayjs().add(4, 'hours').format('DD-MM-YYYY'),
      undefined,
      undefined,
      {},
      undefined,
      undefined
    );
    expect(result.playlist).toEqual(vmapVast4);
  });
});
