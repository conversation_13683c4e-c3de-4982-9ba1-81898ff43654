import { Test, TestingModule } from '@nestjs/testing';
import { getTypeSchema } from 'nestjs-joi';
import { Channel } from 'adpod-tools';
import { PlaylistOutputs } from '../../models/playlistOutput.model';
import { PlaylistMultipleController } from './playlistMultiple.controller';
import { PlaylistMultipleService } from './playlistMultiple.service';
import { PrefetchPlaylistQuery } from './validate/prefetchPlaylistQuery';
import * as setPlaylistResHeaders from '../../scripts/playlist/setPlaylistResHeaders';
import { PrefetchType } from '../../models/prefetch.model';
import { BreaksConfigurationCacheService } from '../../libs/caching/services/breaksConfigurationCache.service';
import { JsonPlaylistService } from '../../scripts/services/createJsonPlaylist.service';
import { CustomParamsGenerator } from '../../scripts/services/customParamsGenerator.service';
import { DaiAdsProviderFactory } from '../../scripts/services/daiAdsProvider/daiAdsProviderFactory';
import { UltimateDaiAdsProvider } from '../../scripts/services/daiAdsProvider/UltimateDaiAdsProvider.service';
import { AdOceanHandler } from '../../scripts/services/daiAdsProvider/AdOcean/AdOceanHandler.service';
import { GoogleAdManagerProvider } from '../../scripts/services/daiAdsProvider/GoogleAdsManager/googleAdManagerDaiAdsProvider.service';
import { FreeWheelDaiAdsProvider } from '../../scripts/services/daiAdsProvider/FreeWheel/freeWheelDaiAdsProvider.service';
import { AdOceanBreakDaiAdsProvider } from '../../scripts/services/daiAdsProvider/AdOcean/AdOceanBreakDaiAdsProvider';
import { AdOceanProxyDaiAdsProvider } from '../../scripts/services/daiAdsProvider/AdOcean/AdOceanProxyDaiAdsProvider';
import {
  IDeapProfilesService,
  DeapProfileService
} from '../../scripts/services/deapProfiles.service';
import { TcfService } from '../../scripts/services/tcf.service';
import {
  FreeWheelFillersAdsProviderService,
  IFreeWheelFillersAdsProviderService
} from '../../scripts/services/daiAdsProvider/FreeWheel/freeWheelFillersAdsProvider.service';
import { FillerAdsService, IFillerAdsService } from 'adpod-aws';
import { DebugService, IDebugService } from '../../libs/caching/services/debug.service';
import { TestCacheModule } from '../../libs/testing';
import { ConfigurationService, IConfigurationService } from './services/configuration.service';
import { WorkerConfigCacheService } from '../../libs';
import { TestAwsModule, TestRedisModule } from 'adpod-aws/dist/testing';
import { PlaylistMerger } from '../../scripts/services/playlist/playlistMerger.service';
import { GeneralPlaylistTransformer } from '../../scripts/services/playlist/generalPlaylistTransformer.service';
import {
  AdSlotPlaylistTransformer,
  IAdSlotPlaylistTransformer
} from '../../scripts/services/playlist/adSlotPlaylistTransformer.service';

describe('PlaylistMultipleController test suite', () => {
  let playlistMultipleController: PlaylistMultipleController;
  let playlistMultipleService: PlaylistMultipleService;

  const schema = getTypeSchema(PrefetchPlaylistQuery);
  jest.spyOn(setPlaylistResHeaders, 'setPlaylistResHeaders').mockResolvedValue();

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TestCacheModule, TestAwsModule, TestRedisModule],
      controllers: [PlaylistMultipleController],
      providers: [
        PlaylistMultipleService,
        PlaylistMerger,
        BreaksConfigurationCacheService,
        WorkerConfigCacheService,
        JsonPlaylistService,
        CustomParamsGenerator,
        DaiAdsProviderFactory,
        UltimateDaiAdsProvider,
        AdOceanHandler,
        GoogleAdManagerProvider,
        FreeWheelDaiAdsProvider,
        AdOceanBreakDaiAdsProvider,
        AdOceanProxyDaiAdsProvider,
        TcfService,
        GeneralPlaylistTransformer,
        {
          provide: IAdSlotPlaylistTransformer,
          useClass: AdSlotPlaylistTransformer
        },
        {
          provide: IFreeWheelFillersAdsProviderService,
          useClass: FreeWheelFillersAdsProviderService
        },
        {
          provide: IFillerAdsService,
          useClass: FillerAdsService
        },
        {
          provide: IDeapProfilesService,
          useClass: DeapProfileService
        },
        {
          provide: IDebugService,
          useClass: DebugService
        },
        {
          provide: IConfigurationService,
          useClass: ConfigurationService
        }
      ]
    }).compile();

    playlistMultipleController = await module.resolve(PlaylistMultipleController);
    playlistMultipleService = await module.resolve(PlaylistMultipleService);
  });

  afterEach(() => {
    jest.resetAllMocks();
    jest.clearAllMocks();
  });

  test('PlaylistMultipleController should be defined', () => {
    expect(playlistMultipleController).toBeDefined();
  });

  test('PlaylistMultipleService to be defined', () => {
    expect(playlistMultipleService).toBeDefined();
  });

  test('PlaylistMultipleController validation; missing ch; validation fails', async () => {
    const queryParams = {
      v: 'nowtilus_v1_0_0',
      output: PlaylistOutputs.vast4,
      mode: PrefetchType.nextDebug
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain('"ch\\" is required');
  });

  test('PlaylistMultipleController validation; empty ch; validation fails', async () => {
    const queryParams = {
      ch: '',
      v: 'nowtilus_v1_0_0',
      mode: PrefetchType.nextDebug
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain('"ch\\" is not allowed to be empty');
  });

  test('PlaylistMultipleController validation; missing v; validation pass', async () => {
    const queryParams = {
      ch: Channel.ttv,
      mode: PrefetchType.nextDebug
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).toBeUndefined();
  });

  test('PlaylistMultipleController validation; empty v; validation pass', async () => {
    const queryParams = {
      ch: Channel.ttv,
      v: '',
      mode: PrefetchType.nextDebug
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).toBeUndefined();
  });

  test('PlaylistMultipleController validation; incorrect output; validation fails', async () => {
    const queryParams = {
      ch: Channel.ttv,
      v: 'nowtilus_v1_0_0',
      mode: PrefetchType.nextDebug,
      output: 'CUSTOM_OUTPUT'
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain('"output\\" must be one of');
  });

  test('PlaylistMultipleController validation; incorrect mode; validation fails', async () => {
    const queryParams = {
      ch: Channel.ttv,
      v: 'nowtilus_v1_0_0',
      mode: 'CUSTOM_MODE'
    };

    const validationResult = schema.validate(queryParams);

    expect(JSON.stringify(validationResult)).toContain('"mode\\" must be one of');
  });

  test('PlaylistMultipleController validation; empty uid; validation fails', async () => {
    const queryParams = {
      ch: Channel.ttv,
      v: 'nowtilus_v1_0_0',
      mode: PrefetchType.nextDebug,
      uid: ''
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain('"uid\\" is not allowed to be empty');
  });

  test('PlaylistMultipleController validation; incorrect npa (string=INCORRECT_NPA); validation fails', async () => {
    const queryParams = {
      ch: Channel.ttv,
      v: 'nowtilus_v1_0_0',
      mode: PrefetchType.nextDebug,
      output: PlaylistOutputs.vast4,
      npa: 'INCORRET_NPA'
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain('"npa\\" must be one of [1, 0]');
  });

  test('PlaylistMultipleController validation; incorrect npa (string=10); validation fails', async () => {
    const queryParams = {
      ch: Channel.ttv,
      v: 'nowtilus_v1_0_0',
      mode: PrefetchType.nextDebug,
      output: PlaylistOutputs.vast4,
      npa: '10'
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain('"npa\\" must be one of [1, 0]');
  });

  test('PlaylistMultipleController validation; incorrect startDate (not a date); validation fails', async () => {
    const queryParams = {
      ch: Channel.ttv,
      v: 'nowtilus_v1_0_0',
      mode: PrefetchType.nextDebug,
      output: PlaylistOutputs.vast4,
      startDate: 'SOME_DATE'
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain(
      '"startDate\\" length must be 25 characters long'
    );
  });

  test('PlaylistMultipleController validation; valid startDate; validation passes', async () => {
    const queryParams = {
      ch: Channel.ttv,
      v: 'nowtilus_v1_0_0',
      mode: PrefetchType.nextDebug,
      output: PlaylistOutputs.vast4,
      startDate: '2022-04-12T12:00:00+00:00'
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).toBeUndefined();
  });

  test('PlaylistMultipleController validation; invalid startDateToken; validation fails', async () => {
    const queryParams = {
      ch: Channel.ttv,
      v: 'nowtilus_v1_0_0',
      mode: PrefetchType.nextDebug,
      output: PlaylistOutputs.vast4,
      startDateToken: '2022-04-12T12:00:00+00:00'
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain('"startDateToken\\" is not valid');
  });
});
