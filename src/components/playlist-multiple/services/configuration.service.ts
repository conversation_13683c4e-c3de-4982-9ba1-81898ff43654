import { Injectable } from '@nestjs/common';
import { Channel, IConfiguration, LogLevel } from 'adpod-tools';
import dayjs from 'dayjs';
import {
  BreaksConfigurationCacheService,
  TimeRange,
  WorkerConfigCacheService
} from '../../../libs';
import { validators } from '../../../EnvValidation/envalidConfig';
import logger from '../../../libs/logging/logger';
import { AWSS3FileContent } from 'adpod-aws';

export abstract class IConfigurationService {
  abstract getConfigurations(
    startDate: dayjs.Dayjs,
    channel: Channel,
    version: string,
    minutes: number,
    allowAnyStartDate: boolean,
    isPrefetchNextMode: boolean,
    startBid?: string
  ): Promise<IConfiguration[]>;
}

@Injectable()
export class ConfigurationService implements IConfigurationService {
  constructor(
    private readonly breaksConfigurationCacheService: BreaksConfigurationCacheService,
    private readonly workerConfigCacheService: WorkerConfigCacheService,
    private readonly awsS3FileContent: AWSS3FileContent
  ) {}

  public async getConfigurations(
    startDate: dayjs.Dayjs,
    channel: Channel,
    version: string,
    minutes: number,
    allowAnyStartDate: boolean,
    isPrefetchNextMode: boolean,
    startBid?: string
  ): Promise<IConfiguration[]> {
    const { outOfRangeConfigsTTL, keepOutOfRangeConfigs } =
      await this.workerConfigCacheService.getScheduleConfigsAvailability();

    const prefetchTimeRange = this.getPrefetchTimeRange(startDate, minutes);

    const isStartBidEnabled = !!startBid && validators.START_BID_S3_FETCH === 'ENABLED';
    logger('PREFETCH_IS_START_BID_ENABLED', { isStartBidEnabled }, LogLevel.debug);

    if (isStartBidEnabled) {
      return this.handleStartBidEnabled(
        isPrefetchNextMode,
        minutes,
        channel,
        version,
        keepOutOfRangeConfigs,
        outOfRangeConfigsTTL,
        startBid
      );
    }

    if (isPrefetchNextMode) {
      return this.handleNextPrefetch(
        isPrefetchNextMode,
        prefetchTimeRange,
        channel,
        version,
        keepOutOfRangeConfigs,
        outOfRangeConfigsTTL
      );
    }

    let cacheConfigs = await this.getCacheConfigsForTimeRange(
      prefetchTimeRange,
      channel,
      version
    );

    const isPrefetchOutOfTimeRange = this.isPrefetchOutOfTimeRange(
      cacheConfigs,
      prefetchTimeRange
    );

    if (!isPrefetchOutOfTimeRange && !allowAnyStartDate) {
      logger(
        'PREFETCH_TIME_IS_IN_RANGE_OF_CACHE',
        {
          isPrefetchOutOfTimeRange: !isPrefetchOutOfTimeRange,
          allowAnyStartDate: !allowAnyStartDate
        },
        LogLevel.debug
      );

      cacheConfigs = cacheConfigs.filter((configuration) =>
        prefetchTimeRange.isDateInTimeRange(configuration.time)
      );

      return this.sortByTime(cacheConfigs);
    }

    return this.handleConfigsFromBucket(
      prefetchTimeRange,
      channel,
      version,
      keepOutOfRangeConfigs,
      outOfRangeConfigsTTL
    );
  }

  private async getConfigsFromBucket(timeRange: TimeRange, channel: Channel, version: string) {
    const dates = timeRange.getAllDatesInRange('days', 'YYYYMMDD');
    logger('PREFETCH_GETTING_CONFIGS_FROM_BUCKET', { dates }, LogLevel.warn);

    const configs = await Promise.all(
      dates.map(async (date) =>
        this.awsS3FileContent.getConfigFromBucket(date, channel, version)
      )
    );

    const configsFromBucket = configs.filter((config) => !!config).flat();
    logger(
      'PREFETCH_CONFIGS_FROM_BUCKET',
      { configsCount: configsFromBucket.length, dates },
      LogLevel.debug
    );

    return configsFromBucket;
  }

  private getPrefetchTimeRange(startDate: dayjs.Dayjs, minutes: number) {
    const endDate = startDate.add(minutes, 'minutes');
    const timeRange = TimeRange.create(startDate, endDate);

    logger('PREFETCH_TIME_RANGE', { prefetchTimeRange: timeRange.toFormat() }, LogLevel.debug);

    return timeRange;
  }

  private async getConfigsWithStartBid(
    channel: Channel,
    version: string,
    minutes: number,
    singleConfig: boolean,
    startBid: string,
    keepOutOfRangeConfigs: boolean,
    outOfRangeConfigsTTL: number
  ) {
    const cacheBucketFetchDaysLimit = validators.CACHE_BUCKET_FETCH_DAYS_LIMIT;
    const pastTimeRange = TimeRange.create(
      dayjs().subtract(cacheBucketFetchDaysLimit, 'day'),
      dayjs().endOf('day')
    );

    logger(
      'PREFETCH_START_BID_PAST_TIME_RANGE',
      { pastTimeRange: pastTimeRange.toFormat(), singleConfig },
      LogLevel.debug
    );

    let configurations = await this.breaksConfigurationCacheService.getConfigsFromCache(
      channel,
      version
    );

    const { startBidTimeRange } = this.getStartBidTimeRange(startBid, minutes, configurations);

    const isPrefetchOutOfTimeRange =
      startBidTimeRange && this.isPrefetchOutOfTimeRange(configurations, startBidTimeRange);

    logger(
      'PREFETCH_START_BID_AND_IS_PREFETCH_OUT_OF_TIME_RANGE',
      { startBidTimeRange, isPrefetchOutOfTimeRange },
      LogLevel.debug
    );

    if (startBidTimeRange && !isPrefetchOutOfTimeRange) {
      const filteredConfigs = this.filterConfigsForStartBid(
        startBid,
        minutes,
        configurations,
        singleConfig
      );

      return filteredConfigs;
    }

    configurations = await this.getConfigsFromBucket(pastTimeRange, channel, version);

    const filteredConfigs = this.filterConfigsForStartBid(
      startBid,
      minutes,
      configurations,
      singleConfig
    );

    if (filteredConfigs.length > 0) {
      await this.breaksConfigurationCacheService.setConfigToCache(
        configurations,
        keepOutOfRangeConfigs,
        outOfRangeConfigsTTL
      );
    }

    logger(
      'PREFETCH_START_BID_FILTERED_CONFIGS',
      { configsCount: filteredConfigs.length },
      LogLevel.debug
    );

    return filteredConfigs;
  }

  private filterConfigsForStartBid(
    startBid: string,
    minutes: number,
    configurations: IConfiguration[],
    singleConfig: boolean
  ) {
    const { startBidIndex, startBidTimeRange } = this.getStartBidTimeRange(
      startBid,
      minutes,
      configurations
    );

    if (!startBidTimeRange || startBidIndex === -1) {
      logger(
        'PREFETCH_START_BID_TIME_RANGE_OR_INDEX_EMPTY',
        { timeRange: startBidTimeRange?.toFormat(), startBidIndex },
        LogLevel.debug
      );
      return [];
    }

    if (singleConfig) {
      logger(
        'PREFETCH_START_BID_SINGLE_CONFIG',
        { singleConfig, startBidIndex },
        LogLevel.debug
      );
      return configurations.slice(startBidIndex + 1, startBidIndex + 2);
    }

    logger(
      'PREFETCH_START_BID_TIME_RANGE',
      { timeRange: startBidTimeRange.toFormat() },
      LogLevel.debug
    );

    return configurations.filter((configuration) =>
      startBidTimeRange.isDateInTimeRange(configuration.time)
    );
  }

  private getStartBidTimeRange(
    startBid: string,
    minutes: number,
    configurations: IConfiguration[]
  ) {
    const startBidIndex = configurations.findIndex((c) => c.id === startBid);

    if (startBidIndex === -1) {
      return { startBidIndex, startBidTimeRange: undefined };
    }

    const startAt = dayjs(configurations[startBidIndex].time);
    const startBidTimeRange = TimeRange.create(startAt, startAt.add(minutes, 'minutes'));

    return { startBidIndex, startBidTimeRange };
  }

  private sortByTime(configs: IConfiguration[]) {
    return configs.sort((a, b) => {
      return dayjs(a.time).diff(dayjs(b.time));
    });
  }

  private async handleNextPrefetch(
    isPrefetchNextMode: boolean,
    prefetchTimeRange: TimeRange,
    channel: Channel,
    version: string,
    keepOutOfRangeConfigs: boolean,
    outOfRangeConfigsTTL: number
  ) {
    logger('IS_PREFETCH_NEXT_MODE', { isPrefetchNextMode }, LogLevel.debug);
    let configurations = (
      await this.breaksConfigurationCacheService.getConfigsFromCache(channel, version)
    )
      .filter((configuration) => prefetchTimeRange.isDateInTimeRange(configuration.time))
      .slice(0, 1);

    if (configurations.length === 0) {
      const configsFromBucket = await this.getConfigsFromBucket(
        prefetchTimeRange,
        channel,
        version
      );

      configurations = configsFromBucket
        .filter((configuration) => prefetchTimeRange.isDateInTimeRange(configuration.time))
        .slice(0, 1);

      if (configurations.length > 0) {
        await this.filterByWholeHourAndSetToCache(
          configsFromBucket,
          prefetchTimeRange,
          keepOutOfRangeConfigs,
          outOfRangeConfigsTTL
        );
      }
    }

    return this.sortByTime(configurations);
  }

  private async handleStartBidEnabled(
    isPrefetchNextMode: boolean,
    minutes: number,
    channel: Channel,
    version: string,
    keepOutOfRangeConfigs: boolean,
    outOfRangeConfigsTTL: number,
    startBid: string
  ) {
    logger('PREFETCH_GETTING_CONFIGS_WITH_START_BID', undefined, LogLevel.debug);
    const configurations = await this.getConfigsWithStartBid(
      channel,
      version,
      minutes,
      isPrefetchNextMode,
      startBid,
      keepOutOfRangeConfigs,
      outOfRangeConfigsTTL
    );

    return this.sortByTime(configurations);
  }

  private async handleConfigsFromBucket(
    prefetchTimeRange: TimeRange,
    channel: Channel,
    version: string,
    keepOutOfRangeConfigs: boolean,
    outOfRangeConfigsTTL: number
  ) {
    logger('PREFETCH_GETTING_CONFIGS_FROM_S3', undefined, LogLevel.debug);
    const configsFromBucket = await this.getConfigsFromBucket(
      prefetchTimeRange,
      channel,
      version
    );

    const configurations = configsFromBucket.filter((configuration) =>
      prefetchTimeRange.isDateInTimeRange(configuration.time)
    );

    if (configurations.length > 0) {
      await this.filterByWholeHourAndSetToCache(
        configsFromBucket,
        prefetchTimeRange,
        keepOutOfRangeConfigs,
        outOfRangeConfigsTTL
      );
    }

    logger(
      'PREFETCH_CONFIGS_RETURNED',
      { configsCount: configurations.length },
      LogLevel.debug
    );

    return this.sortByTime(configurations);
  }

  private isPrefetchOutOfTimeRange(configurations: IConfiguration[], timeRange: TimeRange) {
    const configsTimeRange = this.buildTimeRangeFromConfigs(configurations);

    logger(
      'PREFETCH_CONFIGS_TIME_RANGE',
      { configsTimeRange: configsTimeRange.toFormat() },
      LogLevel.debug
    );

    return !timeRange.isContainedIn(configsTimeRange);
  }

  private buildTimeRangeFromConfigs(configurations: IConfiguration[]) {
    const sortedConfigurations = this.sortByTime(configurations);

    const firstConfiguration = sortedConfigurations[0];
    const lastConfiguration = sortedConfigurations[sortedConfigurations.length - 1];

    return TimeRange.create(dayjs(firstConfiguration.time), dayjs(lastConfiguration.time));
  }

  private async getCacheConfigsForTimeRange(
    timeRange: TimeRange,
    channel: Channel,
    version: string
  ) {
    const dayTimeRange = timeRange.createDayTimeRangeBasedOnDate('days');

    logger(
      'PREFETCH_GETTING_CONFIGS_FROM_CACHE_FOR_DAY_TIME_RANGE',
      { dayTimeRange: dayTimeRange.toFormat() },
      LogLevel.debug
    );

    const cacheConfigs = await this.breaksConfigurationCacheService.getConfigsFromCache(
      channel,
      version
    );

    return cacheConfigs;
  }

  private async filterByWholeHourAndSetToCache(
    configurations: IConfiguration[],
    timeRange: TimeRange,
    keepOutOfRangeConfigs: boolean,
    outOfRangeConfigsTTL: number
  ) {
    const hourTimeRange = timeRange.createDayTimeRangeBasedOnDate('hours');

    const configsFromBucketFilteredByTimeRange = configurations.filter((configuration) =>
      hourTimeRange.isDateInTimeRange(configuration.time)
    );

    await this.breaksConfigurationCacheService.setConfigToCache(
      configsFromBucketFilteredByTimeRange,
      keepOutOfRangeConfigs,
      outOfRangeConfigsTTL
    );
  }
}
