import { Test, TestingModule } from '@nestjs/testing';
import { ConfigurationService, IConfigurationService } from './configuration.service';
import {
  BreaksConfigurationCacheService,
  ICacheProvider,
  WorkerConfigCacheService
} from '../../../libs';
import dayjs from 'dayjs';
import { Channel, IConfiguration } from 'adpod-tools';
import getPrefetchTimeByPrefetchMode from '../../../scripts/vast/getPrefetchTimeByPrefetchMode';
import { PrefetchType } from '../../../models/prefetch.model';
import MockDate from 'mockdate';
import { CacheStub, TestStubCacheModule } from '../../../libs/testing';
import { AWSS3FileContentMock, TestAwsModule } from 'adpod-aws/dist/testing';
import { AWSS3FileContent } from 'adpod-aws';

describe('ConfigurationService', () => {
  let cacheProviderStub: CacheStub;
  let configurationService: IConfigurationService;
  let awsFileContentMock: AWSS3FileContentMock;

  const channel = Channel.tvn;
  const version = 'v1_0_0';
  let startDate = dayjs();

  beforeEach(async () => {
    jest.resetAllMocks();
    jest.clearAllMocks();

    MockDate.set(dayjs('2025-05-07T10:00:00+02:00').toDate());
    startDate = dayjs('2025-05-07T10:00:00+02:00');

    const app: TestingModule = await Test.createTestingModule({
      imports: [TestStubCacheModule, TestAwsModule],
      providers: [
        BreaksConfigurationCacheService,
        WorkerConfigCacheService,
        {
          provide: IConfigurationService,
          useClass: ConfigurationService
        }
      ]
    }).compile();

    cacheProviderStub = app.get(ICacheProvider);
    awsFileContentMock = app.get(AWSS3FileContent);
    configurationService = app.get(IConfigurationService);

    await cacheProviderStub.set('workerConfig', {
      scheduleConfigsAvailability: {
        last: 1,
        next: 2,
        keepOutOfRangeConfigs: false,
        outOfRangeConfigsTTL: 4
      }
    });
  });

  describe('start bid', () => {
    test('should return signle configuration from cache based on start bid', async () => {
      // arrange
      const configs = [
        {
          id: 'config-1',
          time: startDate.add(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: startDate.add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-3',
          time: startDate.add(1, 'hour').add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      await seedCache(configs);

      // act
      const result = await configurationService.getConfigurations(
        startDate,
        channel,
        version,
        getPrefetchTimeByPrefetchMode(PrefetchType.hours1, 4).minutes!,
        false,
        true,
        'config-2'
      );

      // assert
      expect(result).toHaveLength(1);
      const cache = await cacheProviderStub.getMany(
        configs.map((c) => createKey(dayjs(c.time), c.channel, c.version))
      );
      expect(cache).toHaveLength(3);
    });
  });

  describe('next prefetch', () => {
    test('should return the first configuration from cache in prefetch time', async () => {
      // arrange
      const configs = [
        {
          id: 'config-1',
          time: startDate.add(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: startDate.add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-3',
          time: startDate.add(1, 'hour').add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      await seedCache(configs);

      // act
      const result = await configurationService.getConfigurations(
        startDate.add(1, 'hour'),
        channel,
        version,
        getPrefetchTimeByPrefetchMode(PrefetchType.hours1, 4).minutes!,
        false,
        true
      );

      // assert
      expect(result).toHaveLength(1);
      expect(result).toMatchObject([
        {
          id: 'config-3'
        }
      ]);
    });

    test('should return signle configuration from bucket in prefetch time and set it to cache', async () => {
      // arrange
      await cacheProviderStub.set('workerConfig', {
        scheduleConfigsAvailability: {
          last: 1,
          next: 2,
          keepOutOfRangeConfigs: true,
          outOfRangeConfigsTTL: 4
        }
      });

      const configs = [
        {
          id: 'config-1',
          time: startDate.add(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      const bucketConfigs = [
        {
          id: 'config-2',
          time: startDate.add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-3',
          time: startDate.add(1, 'hour').add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-4',
          time: startDate.add(1, 'hour').add(45, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      await seedCache(configs);
      awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce(bucketConfigs);

      // act
      const result = await configurationService.getConfigurations(
        startDate.add(1, 'hour'),
        channel,
        version,
        getPrefetchTimeByPrefetchMode(PrefetchType.hours1, 4).minutes!,
        false,
        true
      );

      // assert
      expect(result).toHaveLength(1);
      expect(result).toMatchObject([
        {
          id: 'config-3'
        }
      ]);
      expect(
        await cacheProviderStub.get(
          createKey(
            dayjs(bucketConfigs[1].time),
            bucketConfigs[1].channel,
            bucketConfigs[1].version
          )
        )
      ).toHaveLength(2);
    });
  });

  describe('non prefetch', () => {
    test('should return two configurations from cache for 30m', async () => {
      // arrange
      const configs = [
        {
          id: 'config-1',
          time: startDate.add(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: startDate.add(20, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-3',
          time: startDate.add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-4',
          time: startDate.add(1, 'hour').add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      await seedCache(configs);

      // act
      const result = await configurationService.getConfigurations(
        startDate.add(19, 'minutes'),
        channel,
        version,
        getPrefetchTimeByPrefetchMode(PrefetchType.minutes30Mixed, 4).minutes!,
        false,
        false
      );

      // assert
      expect(result).toHaveLength(2);
      expect(result).toMatchObject([{ id: 'config-2' }, { id: 'config-3' }]);
    });

    test('should return all configurations from bucket for 300m and set to cache', async () => {
      // arrange
      await cacheProviderStub.set('workerConfig', {
        scheduleConfigsAvailability: {
          last: 1,
          next: 2,
          keepOutOfRangeConfigs: true,
          outOfRangeConfigsTTL: 4
        }
      });

      const configs = [
        {
          id: 'config-1',
          time: startDate.add(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: startDate.add(20, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      const bucketConfigs = [
        {
          id: 'config-0',
          time: startDate.subtract(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-1',
          time: startDate.add(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: startDate.add(20, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-3',
          time: startDate.add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-4',
          time: startDate.add(1, 'hour').add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      await seedCache(configs);
      awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce(bucketConfigs);
      awsFileContentMock.getConfigFromBucket.mockResolvedValue([]);

      // act
      const result = await configurationService.getConfigurations(
        startDate,
        channel,
        version,
        getPrefetchTimeByPrefetchMode(PrefetchType.hours24hMixed, 4).minutes!,
        false,
        false
      );

      // assert
      expect(result).toHaveLength(4);
      expect(result).toMatchObject([
        { id: 'config-1' },
        { id: 'config-2' },
        { id: 'config-3' },
        { id: 'config-4' }
      ]);
      expect(await getCacheItems([bucketConfigs[1], bucketConfigs[4]])).toHaveLength(4);
    });
  });

  function createKey(date: dayjs.Dayjs, ch: Channel, v: string) {
    return `${date.set('minutes', 0).set('seconds', 0).format('YYYYMMDD_HH:mm:ss')}_${ch}_${v}_REQUEST_CACHE_BREAK_KEYS`;
  }

  async function seedCache(configs: IConfiguration[]) {
    const configsGroupedByChannelAndVersion = configs.reduce<Record<string, IConfiguration[]>>(
      (acc, config) => {
        const key = createKey(dayjs(config.time), config.channel, config.version);
        acc[key] = acc[key] ?? [];
        acc[key].push(config);

        return acc;
      },
      {}
    );

    await cacheProviderStub.setMany(
      Object.entries(configsGroupedByChannelAndVersion).map(([key, value]) => ({
        key,
        value
      }))
    );
  }

  async function getCacheItems(configs: IConfiguration[]) {
    const configsGroupedByChannelAndVersion = configs.reduce<Record<string, IConfiguration[]>>(
      (acc, config) => {
        const key = createKey(dayjs(config.time), config.channel, config.version);
        acc[key] = acc[key] ?? [];
        acc[key].push(config);

        return acc;
      },
      {}
    );

    return (
      await cacheProviderStub.getMany(
        Object.entries(configsGroupedByChannelAndVersion).map(([key]) => key)
      )
    ).flat();
  }
});
