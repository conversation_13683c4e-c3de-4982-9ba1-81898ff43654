import { Test } from '@nestjs/testing';
import { InfoModule } from './info.module';
import { InfoService } from './info.service';

describe('InfoModule test suite', () => {
  test('should compile the module', async () => {
    const module = await Test.createTestingModule({
      imports: [InfoModule]
    }).compile();

    expect(module).toBeDefined();
    expect(module.get(InfoModule)).toBeInstanceOf(InfoModule);
    expect(module.get(InfoService)).toBeInstanceOf(InfoService);
  });
});
