import { Controller, Get, HttpException, HttpStatus, Req } from '@nestjs/common';
import { LogLevel } from 'adpod-tools';
import { InfoService } from './info.service';
import { Request, IAppInfo } from '../../interfaces';
import logger from '../../libs/logging/logger';

@Controller('info')
export class InfoController {
  constructor(private readonly infoService: InfoService) {}

  @Get('/ua')
  userAgent(@Req() req: Request): any {
    try {
      return req?.req?.useragent;
    } catch (e: unknown) {
      logger('ERROR_UNKNOWN_SERVER_ERROR', { e }, LogLevel.error);
      if (e instanceof HttpException) {
        throw e;
      }

      if (e instanceof Error) {
        throw new HttpException(e.message, 500);
      }

      throw new HttpException('UNKNOWN ERROR', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('/app')
  appInfo(): IAppInfo {
    try {
      return this.infoService.appInfo;
    } catch (e: unknown) {
      logger('ERROR_UNKNOWN_SERVER_ERROR', { e }, LogLevel.error);
      if (e instanceof HttpException) {
        throw e;
      }

      if (e instanceof Error) {
        throw new HttpException(e.message, 500);
      }

      throw new HttpException('UNKNOWN ERROR', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Get('/check')
  appCheck(): string {
    try {
      return this.infoService.healthCheck();
    } catch (e: unknown) {
      logger('ERROR_UNKNOWN_SERVER_ERROR', { e }, LogLevel.error);
      if (e instanceof HttpException) {
        throw e;
      }

      if (e instanceof Error) {
        throw new HttpException(e.message, HttpStatus.INTERNAL_SERVER_ERROR);
      }

      throw new HttpException('UNKNOWN ERROR', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
