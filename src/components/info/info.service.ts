import os from 'os';
import process from 'process';
import { HttpException, HttpStatus } from '@nestjs/common';
import pckg from '../../../package.json';
import { IAppInfo } from '../../interfaces';

import { healthLimit } from '../../scripts/configuration/healthUtils';
import { validators } from '../../EnvValidation/envalidConfig';

// eslint-disable-next-line @typescript-eslint/no-var-requires
const cluster = require('cluster');

const workerStartedTime = new Date();

export class InfoService {
  get appInfo(): IAppInfo {
    return {
      maxCpu: this.cpuQuantity,
      version: this.appVersion,
      nodeVersion: this.nodeVersion,
      environment: this.environment,
      currentMachineTime: this.currentMachineTime,
      workerStartedTime
    };
  }

  get cpuQuantity(): number {
    return os.cpus().length;
  }

  get nodeVersion(): string {
    return process.version;
  }

  get appVersion(): string {
    return pckg.version;
  }

  get environment(): string | undefined {
    return validators.NODE_ENV;
  }

  get currentMachineTime(): Date {
    return new Date();
  }

  get workerStartedTime(): Date {
    return workerStartedTime;
  }

  healthCheck(): string {
    if (cluster.isMaster) {
      const result: { cluster: number; state: string }[] = [];

      for (const i in cluster.workers) {
        if (cluster.workers[i].process.pid !== process.pid) {
          const { id, state } = cluster.workers[i];

          const current = {
            cluster: id,
            state
          };

          result.push(current);
        }
      }

      const liveWorkers = result.filter((y) => y.state === 'listening').length;
      const needToBe = healthLimit(result);

      if (liveWorkers > needToBe) {
        return JSON.stringify(result);
      }
      throw new HttpException('HEALTH_CHECK_ERROR', HttpStatus.BAD_REQUEST);
    } else {
      return 'OK';
    }
  }
}
