import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { PlaylistSingleController } from './playlistSingle.controller';
import { PlaylistSingleService } from './playlistSingle.service';
import { PrerollService } from './services/preRoll.service';
import { WhatsonService } from '../whatson/whatson.service';
import {
  BreaksConfigurationCacheService,
  comprehensiveLoggingMiddleware,
  requestContextMiddleware
} from '../../libs';
import { JsonPlaylistService } from '../../scripts/services/createJsonPlaylist.service';
import { CustomParamsGenerator } from '../../scripts/services/customParamsGenerator.service';
import { DaiAdsProviderFactory } from '../../scripts/services/daiAdsProvider/daiAdsProviderFactory';
import { AdOceanBreakDaiAdsProvider } from '../../scripts/services/daiAdsProvider/AdOcean/AdOceanBreakDaiAdsProvider';
import { AdOceanHandler } from '../../scripts/services/daiAdsProvider/AdOcean/AdOceanHandler.service';
import { AdOceanProxyDaiAdsProvider } from '../../scripts/services/daiAdsProvider/AdOcean/AdOceanProxyDaiAdsProvider';
import { UltimateDaiAdsProvider } from '../../scripts/services/daiAdsProvider/UltimateDaiAdsProvider.service';
import { FreeWheelDaiAdsProvider } from '../../scripts/services/daiAdsProvider/FreeWheel/freeWheelDaiAdsProvider.service';
import { GoogleAdManagerProvider } from '../../scripts/services/daiAdsProvider/GoogleAdsManager/googleAdManagerDaiAdsProvider.service';
import {
  DeapProfileService,
  IDeapProfilesService
} from '../../scripts/services/deapProfiles.service';
import { TcfService } from '../../scripts/services/tcf.service';
import {
  FreeWheelFillersAdsProviderService,
  IFreeWheelFillersAdsProviderService
} from '../../scripts/services/daiAdsProvider/FreeWheel/freeWheelFillersAdsProvider.service';
import { FillerAdsService, IFillerAdsService } from 'adpod-aws';
import { ConfigurationService, IConfigurationService } from './services/configuration.service';
import { PlaylistMerger } from '../../scripts/services/playlist/playlistMerger.service';
import { GeneralPlaylistTransformer } from '../../scripts/services/playlist/generalPlaylistTransformer.service';
import {
  AdSlotPlaylistTransformer,
  IAdSlotPlaylistTransformer
} from '../../scripts/services/playlist/adSlotPlaylistTransformer.service';

@Module({
  controllers: [PlaylistSingleController],
  providers: [
    PlaylistSingleService,
    PlaylistMerger,
    PrerollService,
    WhatsonService,
    BreaksConfigurationCacheService,
    JsonPlaylistService,
    CustomParamsGenerator,
    DaiAdsProviderFactory,
    UltimateDaiAdsProvider,
    AdOceanHandler,
    GoogleAdManagerProvider,
    FreeWheelDaiAdsProvider,
    AdOceanBreakDaiAdsProvider,
    AdOceanProxyDaiAdsProvider,
    TcfService,
    GeneralPlaylistTransformer,
    {
      provide: IAdSlotPlaylistTransformer,
      useClass: AdSlotPlaylistTransformer
    },
    {
      provide: IFreeWheelFillersAdsProviderService,
      useClass: FreeWheelFillersAdsProviderService
    },
    {
      provide: IFillerAdsService,
      useClass: FillerAdsService
    },
    {
      provide: IDeapProfilesService,
      useClass: DeapProfileService
    },
    {
      provide: IConfigurationService,
      useClass: ConfigurationService
    }
  ]
})
export class PlaylistSingleModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(requestContextMiddleware).forRoutes('*');
    consumer.apply(comprehensiveLoggingMiddleware).forRoutes('*');
  }
}
