import { applyDecorators } from '@nestjs/common';
import { ApiResponse, ApiHeader, ApiProduces, ApiQuery } from '@nestjs/swagger';
import { PlaylistOutputs } from '../../models/playlistOutput.model';
import { PlaylistMode } from '../../models/playlistMode.model';

export function GetDoc(): ReturnType<typeof applyDecorators> {
  return applyDecorators(
    ApiQuery({
      name: 'bid',
      required: true,
      description: 'Configuration id.',
      type: 'string'
    }),
    ApiQuery({
      name: 'ch',
      required: true,
      description: 'Broadcast channel id.',
      type: 'string'
    }),
    ApiQuery({
      name: 'v',
      required: false,
      description: 'Config version. Default value: *v1_0_0*',
      type: 'string'
    }),
    ApiQuery({
      name: 'mode',
      required: false,
      description:
        'Playlist Mode. Possible values:\n\n* mirrored - mirrored ads only.\n\n* debug - mirrored + AdServer ads + extra *&lt;Debug&gt;* tag. If it cannot match AdServer ads it returns mirrored ads.\n\n* mixed - mirrored + AdServer ads. If it cannot match AdServer ads it returns mirrored ads.\n\ndefault - mirrored + AdServer ads. If cannot match AdServer ads it returns empty VAST document.',
      type: 'string',
      enum: PlaylistMode
    }),
    ApiQuery({
      name: 'output',
      required: false,
      description: 'Output format.',
      enum: PlaylistOutputs,
      schema: { default: PlaylistOutputs.default }
    }),
    ApiQuery({
      name: 'cust_params',
      required: false,
      description:
        'Encoded key-values pairs separated by & (%26). If present the parameter will be used in adserver request.',
      type: 'string',
      example: 'paramOne%3D%27valueOne%27%26paramTwo%3DvalueTwo',
      schema: { default: '' }
    }),
    ApiQuery({
      name: 'uid',
      required: false,
      description: 'Unique user ID.',
      type: 'string'
    }),
    ApiQuery({
      name: 'npa',
      required: false,
      description: 'Non-personalised ads',
      enum: ['0', '1']
    }),
    ApiQuery({
      name: 'duration',
      required: false,
      description:
        'Expected playlist duration in seconds. If playlist has different duration than the *duration* param value - empty vast will be returned.',
      type: 'number'
    }),
    ApiHeader({
      name: 'x-device-user-agent',
      required: false,
      description: 'Client’s device user agent.'
    }),
    ApiHeader({
      name: 'x-device-IP',
      required: false,
      description: 'Client’s device IP address.'
    }),
    ApiHeader({
      name: 'x-device-referrer',
      required: false,
      description: 'Client’s device referrer URL.'
    }),
    ApiHeader({
      name: 'x-tvn-links-response-proto',
      required: false,
      description:
        "AdServer and tracking URL's protocol. Request protocol is used if not provided."
    }),
    ApiProduces('application/xml'),
    ApiResponse({ status: 200, description: 'Valid response.' }),
    ApiResponse({ status: 304, description: 'Nothing was changed.' }),
    ApiResponse({ status: 400, description: 'Bad request.' }),
    ApiResponse({ status: 500, description: 'Server error.' })
  );
}
