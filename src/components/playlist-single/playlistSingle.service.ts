import { IConfiguration, xmlParser, Channel, LogLevel } from 'adpod-tools';
import { Injectable } from '@nestjs/common';
import { IPlaylistResponse, RequestHeaders, VastJsonPlaylistType } from '../../interfaces';
import { createEmptyVast } from '../../scripts/vast/createEmptyVast';
import { PlaylistOutputs } from '../../models/playlistOutput.model';
import { PlaylistMode } from '../../models/playlistMode.model';
import { injectReqParams } from '../../scripts/configuration/injectReqParams/injectReqParams';
import { RequestMacroParams } from '../../scripts/configuration/injectReqParams/requestMacroParams';
import { isPlaylistModeDefault } from '../../scripts/playlist/isPlaylistModeDefault';
import { generatePlaylistBreakInfo } from '../../scripts/logs/generatePlaylistBreakInfo';
import logger from '../../libs/logging/logger';
import { JsonPlaylistService } from '../../scripts/services/createJsonPlaylist.service';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { IDebugService } from '../../libs/caching/services/debug.service';
import { IConfigurationService } from './services/configuration.service';
import { PlaylistMerger } from '../../scripts/services/playlist/playlistMerger.service';
import { GeneralPlaylistTransformer } from '../../scripts/services/playlist/generalPlaylistTransformer.service';

dayjs.extend(isBetween);
dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable()
export class PlaylistSingleService {
  constructor(
    private readonly jsonPlaylist: JsonPlaylistService,
    private readonly debugService: IDebugService,
    private readonly configurationService: IConfigurationService,
    private readonly playlistMerger: PlaylistMerger,
    private readonly generalPlaylistTransformer: GeneralPlaylistTransformer
  ) {}

  public async createSingleBreakVastPlaylist(
    requestMacroParams: RequestMacroParams,
    version: string,
    custParams?: string,
    bidDate?: string,
    channel = Channel.ttv,
    output = PlaylistOutputs.default,
    mode: PlaylistMode = PlaylistMode.default,
    headers: RequestHeaders = {}, // IncomingHttpHeaders
    bid?: string,
    duration?: string,
    time?: string,
    ip?: string,
    ua?: string | string[]
  ): Promise<IPlaylistResponse> {
    const versions = version.split(',');

    const possibleConfigurations = await Promise.all(
      versions.map((v) =>
        this.configurationService.getConfiguration(channel, v, mode, time, bid, bidDate)
      )
    );

    let configurations = possibleConfigurations
      .filter((config) => config !== null)
      .map((configuration) =>
        injectReqParams(configuration, requestMacroParams, headers, ip, ua)
      ) as IConfiguration[];

    const requestLog = {
      bid,
      channel,
      configVersion: version,
      mode,
      output,
      requestMacroParams
    };

    logger(
      'GET_PLAYLIST_START',
      {
        ...requestLog,
        headers,
        injectedParams: requestMacroParams,
        custParams
      },
      LogLevel.dev
    );

    // A lack of invalid configuration
    // so we return empty vast
    if (!configurations.length) {
      const emptyVastReason = 'lack of configuration';
      return {
        playlist: await createEmptyVast(requestLog, emptyVastReason, output),
        emptyVastReason,
        playlistInfo: null,
        requestLog
      };
    }

    if (duration) {
      const breakDurations = configurations.map((c) => c.duration);

      logger(
        'BREAK_DURATION_VALIDATION',
        {
          expectedDuration: duration,
          breakDurations
        },
        LogLevel.dev
      );

      configurations = configurations.filter((c) => c.duration !== parseInt(duration));
      if (!configurations.length) {
        const emptyVastReason = 'break durations different than expected';
        return {
          playlist: await createEmptyVast(requestLog, emptyVastReason, output),
          emptyVastReason,
          playlistInfo: null,
          requestLog
        };
      }
    }

    const configsWithDaiAds = await Promise.all(
      configurations.map(async (configuration) => {
        const breakDetails = await this.jsonPlaylist.create(
          configuration,
          mode,
          channel,
          version,
          requestMacroParams,
          custParams,
          output,
          headers,
          ip,
          ua
        );

        return {
          breakDetails,
          config: configuration
        };
      })
    );

    const finalPlaylist = this.playlistMerger.merge(configsWithDaiAds);

    const { vast4Json, isWithReplacedAds, adServerResponseLog } = finalPlaylist.breakDetails;

    // We didn't change any add, so need to return empty vast (in case of ATV ads)
    if (!isWithReplacedAds && isPlaylistModeDefault(mode)) {
      const emptyVastReason = 'no replaced ads';
      return {
        playlist: await createEmptyVast(requestLog, emptyVastReason, output),
        emptyVastReason,
        playlistInfo: null,
        requestLog
      };
    }

    let playlistVastJson: VastJsonPlaylistType = vast4Json;

    if (output === PlaylistOutputs.default) {
      logger('GET_PLAYLIST_CONVERT_VAST4_TO_VMAP', {}, LogLevel.dev);
      playlistVastJson = await this.generalPlaylistTransformer.convertToVmap(
        [playlistVastJson],
        configurations,
        mode,
        undefined
      );
    }

    logger('GET_PLAYLIST_FINISH', {}, LogLevel.dev);

    const responseDetails = {
      playlist: this.generalPlaylistTransformer.replaceNotAllowedCharsInXml(
        xmlParser.fromJSONtoXML(playlistVastJson, mode === PlaylistMode.debug),
        mode
      ),
      emptyVastReason: null,
      playlistInfo: generatePlaylistBreakInfo(vast4Json, version, channel, adServerResponseLog)
    };

    return {
      ...responseDetails,
      requestLog
    };
  }
}
