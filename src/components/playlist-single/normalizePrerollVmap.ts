import { normalizeVast4 } from 'adpod-tools';
import { PrerollVmap, PrerollVmapNormalized } from '../../interfaces';

export const normalizePrerollVmap = (obj: PrerollVmap): PrerollVmapNormalized => {
  const adData = obj['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource']['vmap:VASTAdData'];

  obj['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource']['vmap:VASTAdData'] =
    normalizeVast4(adData);

  return obj as PrerollVmapNormalized;
};
