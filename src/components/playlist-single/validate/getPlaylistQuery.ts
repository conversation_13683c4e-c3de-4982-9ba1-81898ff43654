import { Channel } from 'adpod-tools';
import { JoiSchema, JoiSchemaOptions } from 'nestjs-joi';
import <PERSON>Joi from 'joi';
import JoiDate from '@joi/date';
import { isValidDateToken } from '../../../validators/isValidDateToken';
import { isValidUriParams } from '../../../validators/isValidUriParams';
import { PlaylistMode } from '../../../models/playlistMode.model';
import { PlaylistOutputs } from '../../../models/playlistOutput.model';
import { isValidPlaylistSingleTime } from '../../../validators/isValidDateTime';

const Joi = BaseJoi.extend(JoiDate);

@JoiSchemaOptions({
  allowUnknown: true,
  cache: true
})
export class GetPlaylistQuery {
  @JoiSchema(Joi.string().optional())
  bid?: string;

  @JoiSchema(
    Joi.valid(...Object.values(PlaylistOutputs))
      .empty('')
      .optional()
  )
  output?: PlaylistOutputs;

  @JoiSchema(
    Joi.valid(...Object.values(PlaylistMode))
      .empty('')
      .optional()
  )
  mode?: PlaylistMode;

  @JoiSchema(Joi.string().min(1).required())
  ch!: Channel;

  @JoiSchema(Joi.string().allow('').empty('').optional())
  v?: string;

  @JoiSchema(Joi.string().min(1).optional())
  uid?: string;

  @JoiSchema(Joi.valid('1', '0').optional())
  gdpr?: string;

  @JoiSchema(Joi.string().min(1).optional())
  gdpr_consent?: string;

  @JoiSchema(Joi.valid('1', '0').empty('').optional())
  npa?: '1' | '0' | undefined;

  @JoiSchema(
    Joi.string().custom(isValidUriParams).empty('').optional().messages({
      'any.invalid': '"cust_params" must be valid string with uri params'
    })
  )
  cust_params?: string;

  @JoiSchema(Joi.date().format('YYYYMMDD').empty('').raw().optional())
  bidDate?: string;

  @JoiSchema(
    Joi.string()
      .custom(isValidDateToken)
      .when('bidDate', {
        is: Joi.exist(),
        then: Joi.required(),
        otherwise: Joi.optional()
      })
      .empty('')
      .messages({
        'any.required': 'validation failed', // Do not show property's name for security reasons
        'any.invalid': '"bidDateToken" is invalid'
      })
  )
  bidDateToken?: string;

  @JoiSchema(Joi.string().min(1).optional())
  duration?: string;

  @JoiSchema(
    Joi.string().custom(isValidPlaylistSingleTime).empty('').optional().messages({
      'any.format': 'Wrong "time" format. Use YYYY-MM-DDTHH:MM:SS+XX:00 format'
    })
  )
  time?: string;

  @JoiSchema(Joi.string().optional())
  ip?: string;

  @JoiSchema(Joi.string().optional())
  ua?: string;
}
