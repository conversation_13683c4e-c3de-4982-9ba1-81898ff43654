import { Test, TestingModule } from '@nestjs/testing';
import { getTypeSchema } from 'nestjs-joi';
import { Channel } from 'adpod-tools';
import { PlaylistOutputs } from '../../models/playlistOutput.model';
import { PlaylistSingleController } from './playlistSingle.controller';
import { PlaylistSingleService } from './playlistSingle.service';
import { GetPlaylistQuery } from './validate/getPlaylistQuery';
import * as setPlaylistResHeaders from '../../scripts/playlist/setPlaylistResHeaders';
import { PrerollService } from './services/preRoll.service';
import { WhatsonService } from '../whatson/whatson.service';
import { WhatsonController } from '../whatson/whatson.controller';
import { BreaksConfigurationCacheService } from '../../libs/caching/services/breaksConfigurationCache.service';
import { JsonPlaylistService } from '../../scripts/services/createJsonPlaylist.service';
import { CustomParamsGenerator } from '../../scripts/services/customParamsGenerator.service';
import { DaiAdsProviderFactory } from '../../scripts/services/daiAdsProvider/daiAdsProviderFactory';
import { AdOceanBreakDaiAdsProvider } from '../../scripts/services/daiAdsProvider/AdOcean/AdOceanBreakDaiAdsProvider';
import { AdOceanHandler } from '../../scripts/services/daiAdsProvider/AdOcean/AdOceanHandler.service';
import { AdOceanProxyDaiAdsProvider } from '../../scripts/services/daiAdsProvider/AdOcean/AdOceanProxyDaiAdsProvider';
import { UltimateDaiAdsProvider } from '../../scripts/services/daiAdsProvider/UltimateDaiAdsProvider.service';
import { FreeWheelDaiAdsProvider } from '../../scripts/services/daiAdsProvider/FreeWheel/freeWheelDaiAdsProvider.service';
import { GoogleAdManagerProvider } from '../../scripts/services/daiAdsProvider/GoogleAdsManager/googleAdManagerDaiAdsProvider.service';
import {
  IDeapProfilesService,
  DeapProfileService
} from '../../scripts/services/deapProfiles.service';
import { TcfService } from '../../scripts/services/tcf.service';
import { FillerAdsService, IFillerAdsService } from 'adpod-aws';
import {
  FreeWheelFillersAdsProviderService,
  IFreeWheelFillersAdsProviderService
} from '../../scripts/services/daiAdsProvider/FreeWheel/freeWheelFillersAdsProvider.service';
import { DebugService, IDebugService } from '../../libs/caching/services/debug.service';
import { TestCacheModule } from '../../libs/testing';
import { ConfigurationService, IConfigurationService } from './services/configuration.service';
import { WorkerConfigCacheService } from '../../libs';
import { GeneralPlaylistTransformer } from '../../scripts/services/playlist/generalPlaylistTransformer.service';
import { PlaylistMerger } from '../../scripts/services/playlist/playlistMerger.service';
import {
  AdSlotPlaylistTransformer,
  IAdSlotPlaylistTransformer
} from '../../scripts/services/playlist/adSlotPlaylistTransformer.service';
import { TestAwsModule, TestRedisModule } from 'adpod-aws/dist/testing';

describe('PlaylistSingleController test suite', () => {
  let playlistSingleController: PlaylistSingleController;
  let playlistSingleService: PlaylistSingleService;

  const schema = getTypeSchema(GetPlaylistQuery);

  jest.spyOn(setPlaylistResHeaders, 'setPlaylistResHeaders').mockResolvedValue();

  beforeEach(async () => {
    jest.resetAllMocks();
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      imports: [TestCacheModule, TestAwsModule, TestRedisModule],
      controllers: [PlaylistSingleController, WhatsonController],
      providers: [
        PlaylistSingleService,
        PlaylistMerger,
        PrerollService,
        WhatsonService,
        BreaksConfigurationCacheService,
        WorkerConfigCacheService,
        JsonPlaylistService,
        CustomParamsGenerator,
        DaiAdsProviderFactory,
        UltimateDaiAdsProvider,
        AdOceanHandler,
        GoogleAdManagerProvider,
        FreeWheelDaiAdsProvider,
        AdOceanBreakDaiAdsProvider,
        AdOceanProxyDaiAdsProvider,
        TcfService,
        GeneralPlaylistTransformer,
        {
          provide: IAdSlotPlaylistTransformer,
          useClass: AdSlotPlaylistTransformer
        },
        {
          provide: IFreeWheelFillersAdsProviderService,
          useClass: FreeWheelFillersAdsProviderService
        },
        {
          provide: IFillerAdsService,
          useClass: FillerAdsService
        },
        {
          provide: IDeapProfilesService,
          useClass: DeapProfileService
        },
        {
          provide: IDebugService,
          useClass: DebugService
        },
        {
          provide: IConfigurationService,
          useValue: ConfigurationService
        }
      ]
    }).compile();

    playlistSingleController = await module.resolve(PlaylistSingleController);
    playlistSingleService = await module.resolve(PlaylistSingleService);
  });

  test('PlaylistSingleController should be defined', () => {
    expect(playlistSingleController).toBeDefined();
  });

  test('PlaylistSingleService to be defined', () => {
    expect(playlistSingleService).toBeDefined();
  });

  test('PlaylistSingleController validation; missing ch; validation fails', async () => {
    const queryParams = {
      bid: '7515635888214321',
      v: 'v1_0_0_daiTest50_ADO_REN',
      output: PlaylistOutputs.vast4
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain('"ch\\" is required');
  });

  test('PlaylistSingleController validation; empty ch; validation fails', async () => {
    const queryParams = {
      bid: '7515635888214321',
      ch: '',
      v: 'v1_0_0_daiTest50_ADO_REN',
      output: PlaylistOutputs.vast4
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain('"ch\\" is not allowed to be empty');
  });

  test('PlaylistSingleController validation; missing v; validation pass', async () => {
    const queryParams = {
      bid: '7515635888214321',
      ch: Channel.ttv,
      output: PlaylistOutputs.vast4
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).toBeUndefined();
  });

  test('PlaylistSingleController validation; empty v; validation pass', async () => {
    const queryParams = {
      bid: '7515635888214321',
      ch: Channel.ttv,
      v: '',
      output: PlaylistOutputs.vast4
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).toBeUndefined();
  });

  test('PlaylistSingleController validation; incorrect output; validation fails', async () => {
    const queryParams = {
      bid: '7515635888214321',
      ch: Channel.ttv,
      v: 'v1_0_0_daiTest50_ADO_REN',
      output: 'CUSTOM_OUTPUT'
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain('"output\\" must be one of');
  });

  test('PlaylistSingleController validation; incorrect mode; validation fails', async () => {
    const queryParams = {
      bid: '7515635888214321',
      ch: Channel.ttv,
      v: 'v1_0_0_daiTest50_ADO_REN',
      mode: 'CUSTOM_OUTPUT'
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain('"mode\\" must be one of');
  });

  test('PlaylistSingleController validation; empty uid; validation fails', async () => {
    const queryParams = {
      bid: '7515635888214321',
      ch: Channel.ttv,
      output: PlaylistOutputs.vast4,
      uid: ''
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain('"uid\\" is not allowed to be empty');
  });

  test('PlaylistSingleController validation; incorrect npa (string=INCORRECT_NPA); validation fails', async () => {
    const queryParams = {
      bid: '7515635888214321',
      ch: Channel.ttv,
      output: PlaylistOutputs.vast4,
      npa: 'INCORRET_NPA'
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain('"npa\\" must be one of [1, 0]');
  });

  test('PlaylistSingleController validation; incorrect npa (string=10); validation fails', async () => {
    const queryParams = {
      bid: '7515635888214321',
      ch: Channel.ttv,
      output: PlaylistOutputs.vast4,
      npa: '10'
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain('"npa\\" must be one of [1, 0]');
  });

  test('PlaylistSingleController validation; incorrect bidDate (not a date); validation fails', async () => {
    const queryParams = {
      bid: '7515635888214321',
      ch: Channel.ttv,
      v: 'v1_0_0_daiTest50_ADO_REN',
      output: PlaylistOutputs.vast4,
      bidDate: 'SOME_DATE'
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain(
      '"bidDate\\" must be in YYYYMMDD format'
    );
  });

  test('PlaylistSingleController validation; empty bidDate; invalid bidDateToken; validation fails', async () => {
    const queryParams = {
      bid: '7515635888214321',
      ch: Channel.ttv,
      v: 'v1_0_0_daiTest50_ADO_REN',
      output: PlaylistOutputs.vast4,
      bidDate: 'xxx'
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain(
      '"bidDate\\" must be in YYYYMMDD format'
    );
  });

  test('PlaylistSingleController validation; valid bidDate; empty bidDateToken; validation fails', async () => {
    const queryParams = {
      bid: '7515635888214321',
      ch: Channel.ttv,
      v: 'v1_0_0_daiTest50_ADO_REN',
      output: PlaylistOutputs.vast4,
      bidDate: '20220101'
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain('validation failed');
  });

  test('PlaylistSingleController validation; valid bidDate; incorrect bidDateToken (not a sha256 hash); validation fails', async () => {
    const queryParams = {
      bid: '7515635888214321',
      ch: Channel.ttv,
      v: 'v1_0_0_daiTest50_ADO_REN',
      output: PlaylistOutputs.vast4,
      bidDate: '20220101',
      bidDateToken: 'SOME_HASH'
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain('"bidDateToken\\" is invalid');
  });

  test('PlaylistSingleController validation; valid bidDate; incorrect bidDateToken (incorrect sha256 hash); validation fails', async () => {
    const queryParams = {
      bid: '7515635888214321',
      ch: Channel.ttv,
      v: 'v1_0_0_daiTest50_ADO_REN',
      output: PlaylistOutputs.vast4,
      bidDate: '20220101',
      bidDateToken: '15b4a3489da7d294d7fe01ce874629e7619d02536512ee7378c1a0a3135b651f'
    };

    const validationResult = schema.validate(queryParams);

    expect(validationResult?.error).not.toBeUndefined();
    expect(JSON.stringify(validationResult)).toContain('"bidDateToken\\" is invalid');
  });
});
