import {
  AdImpression,
  Channel,
  LogLevel,
  RegistryId,
  hmsToMilliseconds,
  request,
  xmlParser,
  AdVast4Normalized,
  AdCreativeNormalized
} from 'adpod-tools';
import { Injectable } from '@nestjs/common';
import { RequestMacroParams } from '../../../scripts/configuration/injectReqParams/requestMacroParams';
import { PlaylistMode } from '../../../models/playlistMode.model';
import { WhatsonResponseType } from '../../whatson/whatson.service';
import logger from '../../../libs/logging/logger';
import { BreaksConfigurationCacheService } from '../../../libs/caching/services/breaksConfigurationCache.service';
import { CustomParamsGenerator } from '../../../scripts/services/customParamsGenerator.service';
import { PrerollEventsParams, PrerollVmap, PrerollVmapNormalized } from '../../../interfaces';
import { normalizePrerollVmap } from '../normalizePrerollVmap';
import { URLParamsHelper } from '../../../scripts/adserver/urlHelper';
import Joi from 'joi';

@Injectable()
export class PrerollService {
  constructor(
    private readonly breaksConfigurationCacheService: BreaksConfigurationCacheService,
    private readonly customParams: CustomParamsGenerator
  ) {}

  private createImpressionEventFullUrl(
    e: string,
    ad: AdVast4Normalized,
    eventParams: PrerollEventsParams
  ): string {
    const { channel, version, requestProtocol, custParamsValue, uid } = eventParams;

    const helper = new URLParamsHelper(`${requestProtocol}://dai-discoveryengage.tvn.pl/`);

    const dai_ad = ad
      .InLine!.Creatives.Creative[0]?.Linear?.MediaFiles?.MediaFile[0]?._cdata?.split('/')
      .slice(-1)[0];
    const adDuration = ad.InLine!.Creatives.Creative[0]?.Linear?.Duration._text ?? '';
    const adDurationMs = hmsToMilliseconds(adDuration);
    const dur = adDurationMs ? `${adDurationMs / 1000}` : '';

    const params = new URLParamsHelper('', '%2F');
    params
      .add('ch', channel)
      .add('v', version)
      .addMaybe('uid', uid)
      .add('e', e)
      .add('mode', PlaylistMode.preroll)
      .addMaybe('dai_ad', dai_ad)
      .addMaybe('dur', dur)
      .addMaybe('c', ad.InLine!.AdTitle._text)
      .add('bt', PlaylistMode.preroll)
      .addMaybeRaw('cust_params', decodeURIComponent(custParamsValue));

    helper.add('ed', params.toString());

    return helper.toString();
  }

  private additionalImpression(
    ad: AdVast4Normalized,
    eventParams: PrerollEventsParams
  ): AdImpression[] {
    return [
      {
        _attributes: { id: '' },
        _cdata: eventParams.slotImpressionUrl
      },
      {
        _attributes: { id: '' },
        _cdata: this.createImpressionEventFullUrl('impression', ad, eventParams)
      }
    ];
  }

  private createTrackingEventFullUrl(
    e: string,
    ad: AdVast4Normalized,
    eventParams: PrerollEventsParams
  ) {
    const { channel, version, requestProtocol, custParamsValue, uid } = eventParams;

    const helper = new URLParamsHelper(`${requestProtocol}://dai-discoveryengage.tvn.pl/`);

    const dai_ad =
      ad
        .InLine!.Creatives.Creative[0]?.Linear?.MediaFiles?.MediaFile[0]?._cdata?.split('/')
        .slice(-1)[0] ?? '';

    const adDuration = ad.InLine!.Creatives.Creative[0]?.Linear?.Duration._text ?? '';
    const adDurationMs = hmsToMilliseconds(adDuration);
    const dur = adDurationMs ? `${adDurationMs / 1000}` : '';

    const params = new URLParamsHelper('', '%2F');
    params
      .add('ch', channel)
      .add('v', version)
      .addMaybe('uid', uid)
      .add('e', e)
      .add('mode', PlaylistMode.preroll)
      .addMaybe('dai_ad', dai_ad)
      .addMaybe('dur', dur)
      .addMaybe('c', ad.InLine!.AdTitle._text)
      .add('bt', PlaylistMode.preroll)
      .addMaybeRaw('cust_params', decodeURIComponent(custParamsValue));

    helper.add('ed', params.toString());

    return helper.toString();
  }

  private additionalTrackings(adVast4: AdVast4Normalized, eventParams: PrerollEventsParams) {
    const quartiles = [
      { event: 'start', value: '0' },
      { event: 'firstQuartile', value: '25' },
      { event: 'midpoint', value: '50' },
      { event: 'thirdQuartile', value: '75' },
      { event: 'complete', value: '100' }
    ];

    return [
      ...quartiles.map(({ event, value }) => ({
        _attributes: { event },
        _cdata: this.createTrackingEventFullUrl(value, adVast4, eventParams)
      })),
      {
        _attributes: { event: 'complete' },
        _cdata: eventParams.slotEndUrl
      }
    ];
  }

  private modifyCreativeAd(
    jsonResponse: PrerollVmapNormalized,
    channel: string,
    content: any
  ) {
    const companionAd: AdCreativeNormalized = {
      CompanionAds: {
        Companion: {
          _attributes: { id: '1' },
          StaticResource: {
            _attributes: { type: 'image/png' },
            _cdata: `https://assets.ad-pod-manager.services.tvn.pl/img/switchin/${channel}.png`
          },
          AdParameters: content ? { EPGMetadata: content.EPGMetadata } : undefined
        }
      }
    };

    jsonResponse['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource']['vmap:VASTAdData'].VAST.Ad =
      jsonResponse['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource'][
        'vmap:VASTAdData'
      ].VAST.Ad.map((adEl) => {
        if (adEl.InLine?.Creatives.Creative?.length) {
          const creativeAdId =
            adEl.InLine.Creatives.Creative[0].Linear?.MediaFiles?.MediaFile[0]?._cdata
              ?.split('/')
              .slice(-1);

          const creativeAdIdNameOnly = creativeAdId?.length
            ? creativeAdId[0].split('.')[0]
            : '';

          const creativeAd: AdCreativeNormalized = {
            ...adEl?.InLine?.Creatives?.Creative[0],
            UniversalAdId: {
              _text: creativeAdIdNameOnly,
              _attributes: {
                idRegistry: RegistryId.di
              }
            },
            _attributes: {
              id: creativeAdIdNameOnly
            }
          };

          return {
            ...adEl,
            InLine: {
              ...adEl.InLine,
              Creatives: {
                Creative: [creativeAd, companionAd]
              }
            }
          };
        }

        return adEl;
      });
    return jsonResponse;
  }

  private modifyImpressions(
    jsonResponse: PrerollVmapNormalized,
    eventParams: PrerollEventsParams
  ) {
    jsonResponse['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource']['vmap:VASTAdData'].VAST.Ad =
      jsonResponse['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource'][
        'vmap:VASTAdData'
      ].VAST.Ad.map((adEl) => {
        if (adEl.InLine?.Impression) {
          adEl.InLine.Impression.push(...this.additionalImpression(adEl, eventParams));
        }
        return adEl;
      });
    return jsonResponse;
  }

  private modifyTrackings(
    jsonResponse: PrerollVmapNormalized,
    eventParams: PrerollEventsParams
  ) {
    jsonResponse['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource']['vmap:VASTAdData'].VAST.Ad =
      jsonResponse['vmap:VMAP']['vmap:AdBreak']['vmap:AdSource'][
        'vmap:VASTAdData'
      ].VAST.Ad.map((adEl) => {
        let newAd = adEl;

        if (adEl.InLine?.Creatives?.Creative) {
          newAd = {
            ...adEl,
            InLine: {
              ...adEl.InLine,
              Creatives: {
                Creative: adEl.InLine.Creatives.Creative.map((creativeEl) => {
                  if (creativeEl.Linear) {
                    creativeEl.Linear.TrackingEvents.Tracking.push(
                      ...this.additionalTrackings(adEl, eventParams)
                    );
                  }

                  return creativeEl;
                })
              }
            }
          };
        }

        return newAd;
      });
    return jsonResponse;
  }

  private prepareParams(
    jsonResponse: PrerollVmapNormalized,
    injectedParams: RequestMacroParams,
    channel: string,
    version: string
  ): PrerollEventsParams {
    const { uid, custParams } = injectedParams;

    const trackingEvents =
      jsonResponse['vmap:VMAP']['vmap:AdBreak']['vmap:TrackingEvents']!['vmap:Tracking'];

    const slotImpressionUrl = trackingEvents.find(
      (e) => e._attributes.event === 'breakStart'
    )?._cdata;

    const slotEndUrl = trackingEvents.find((e) => e._attributes.event === 'breakEnd')?._cdata;

    // handle impression scripts update
    const custParamsValue = custParams ?? '';

    return {
      channel,
      version,
      requestProtocol: injectedParams.requestProtocol,
      uid,
      custParamsValue,
      slotImpressionUrl,
      slotEndUrl
    };
  }

  private modifyEvents(
    jsonResponse: PrerollVmapNormalized,
    injectedParams: RequestMacroParams,
    channel: string,
    version: string
  ) {
    const eventParams = this.prepareParams(jsonResponse, injectedParams, channel, version);

    jsonResponse = this.modifyImpressions(jsonResponse, eventParams);

    jsonResponse = this.modifyTrackings(jsonResponse, eventParams);

    // remove default TrackingEvents
    delete jsonResponse['vmap:VMAP']['vmap:AdBreak']['vmap:TrackingEvents'];

    return jsonResponse;
  }

  private async getConfigDetails(
    channel: Channel,
    version: string
  ): Promise<{
    prerollRequestUrl?: string;
    deapProfiles?: boolean;
    trackingDaiAds?: boolean;
  } | null> {
    const fullConfig = await this.breaksConfigurationCacheService.getConfigsFromCache(
      channel,
      version
    );

    if (fullConfig.length) {
      const findPrerollBreak = fullConfig.find(
        (c) => c.prerollRequestUrl && c.metadata?.preroll
      );

      if (findPrerollBreak) {
        const { prerollRequestUrl } = findPrerollBreak;

        const { deapProfiles, trackingDaiAds } = findPrerollBreak.metadata!;

        return { prerollRequestUrl, deapProfiles, trackingDaiAds };
      }
    }

    return null;
  }

  private async preparePrerollFetchUrl(
    configPrerollUrl: string,
    version: string,
    channel: Channel,
    injectedParams: RequestMacroParams,
    headers: object, // IncomingHttpHeaders,
    hasDeapProfiles?: boolean
  ) {
    let requestURL = injectedParams.applyParams(configPrerollUrl, headers);

    const { uid, custParams } = injectedParams;

    const customParams = await this.customParams.generate(
      version,
      channel,
      custParams,
      uid,
      hasDeapProfiles
    );

    const currentFlagParams = `${new URLSearchParams(requestURL).get('flag')}`;

    requestURL = `${requestURL.replace(
      /&flag=([^&]*)/,
      `&flag=${currentFlagParams.replace(/^:/, '').replace(/ /g, `+`)}&${
        customParams ? decodeURIComponent(customParams) : ''
      }`
    )}`;

    return requestURL;
  }

  private async fetchFreeWheelData(
    prerollUrl: string,
    headers: object // IncomingHttpHeaders
  ): Promise<object | null> {
    logger('PREROLL_REQUEST_URL', { prerollUrl }, LogLevel.dev);
    logger('PREROLL_REQUEST_HEADERS', { headers }, LogLevel.dev);

    try {
      const resData = await request(prerollUrl, {
        headers: {
          'user-agent': headers?.['x-device-user-agent'],
          'x-device-user-agent': headers?.['x-device-user-agent']
        }
      });

      const responseStatus: number = resData.status;
      const responseStatusText: string = resData.statusText;
      const responseHeaders: Headers = resData.headers;
      const responseOk: boolean = resData.ok;
      const responseType: string = resData.type;
      const responseUrl: string = resData.url;

      const responseText: string = await resData.text();

      logger(
        'PREROLL_FW_RESPONSE',
        {
          responseStatus,
          responseStatusText,
          responseHeaders,
          responseOk,
          responseType,
          responseUrl,
          responseText
        },
        LogLevel.dev
      );

      if (+responseStatus > 400) {
        logger('ERROR_PREROLL_FW_RESPONSE_CODE', { responseStatus }, LogLevel.error);
      }

      const xmlToJson = xmlParser.fromXMLtoJSON(responseText);

      logger('PREROLL_FW_XML_TO_JSON', { xmlToJson }, LogLevel.dev);

      return xmlToJson;
    } catch (err) {
      logger('PREROLL_FETCH_ERORR', { err });
    }

    logger('PREROLL_FW_REQUEST_NULL_EXIT');
    return null;
  }

  private validateFreeWheelResponse(jsonResp: object | null): jsonResp is PrerollVmap {
    logger('PREROLL_VALIDATION_START', {}, LogLevel.dev);

    const joiObject = Joi.object({
      'vmap:VMAP': Joi.object({
        _attributes: Joi.object(),

        'vmap:AdBreak': Joi.object({
          _attributes: Joi.object({
            breakType: Joi.string(),
            breakId: Joi.string(),
            timeOffset: Joi.string()
          }),

          'vmap:TrackingEvents': Joi.object({
            'vmap:Tracking': Joi.array()
          }),

          'vmap:AdSource': Joi.object({
            _attributes: Joi.object(),

            'vmap:VASTAdData': Joi.object({
              VAST: Joi.object({
                _attributes: Joi.object(),

                Ad: Joi.alternatives(Joi.object(), Joi.array()).required()
              })
            })
          })
        })
      })
    })
      .options({
        abortEarly: false
      })
      .validate(jsonResp);

    if (joiObject.error) {
      logger('ERROR_PREROLL_VALIDATION', { error: joiObject.error }, LogLevel.error);
      return false;
    }

    logger('PREROLL_VALIDATION_SCHEMA', { schema: joiObject.value }, LogLevel.dev);
    return true;
  }

  private modifyFreewheelResponse(
    jsonResponse: PrerollVmapNormalized,
    injectedParams: RequestMacroParams,
    channel: string,
    version: string,
    content?: any
  ): PrerollVmapNormalized {
    logger('PREROLL_FW_RESPONSE_MODIFY', { content }, LogLevel.dev);

    jsonResponse = this.modifyCreativeAd(jsonResponse, channel, content);

    jsonResponse['vmap:VMAP']['vmap:AdBreak']._attributes.breakType = 'preroll';

    jsonResponse = this.modifyEvents(jsonResponse, injectedParams, channel, version);

    return jsonResponse;
  }

  public async createPrerollResponse(
    channel: Channel,
    version: string,
    injectedParams: RequestMacroParams,
    headers: object, // IncomingHttpHeaders,
    mode: PlaylistMode,
    whatsonResponse: WhatsonResponseType
  ): Promise<string> {
    const configData = await this.getConfigDetails(channel, version);

    if (configData) {
      const {
        prerollRequestUrl,
        deapProfiles: hasDeapProfiles,
        trackingDaiAds: hasAdditionalTrackings
      } = configData;

      if (!hasAdditionalTrackings) {
        return '';
      }

      const requestURL = await this.preparePrerollFetchUrl(
        prerollRequestUrl as string,
        version,
        channel,
        injectedParams,
        headers,
        hasDeapProfiles
      );

      const jsonResponse = await this.fetchFreeWheelData(requestURL, headers);

      const validationResult = this.validateFreeWheelResponse(jsonResponse);

      logger('PREROLL_VALIDATION_RESULT', { validationResult }, LogLevel.dev);

      if (validationResult) {
        const vmapNormalized = normalizePrerollVmap(jsonResponse);

        const updatedResponse = this.modifyFreewheelResponse(
          vmapNormalized,
          injectedParams,
          channel,
          version,
          whatsonResponse?.content
        );

        // handle debug mode
        if (mode === PlaylistMode.preroll_debug) {
          const debugTag = {
            Debug: {
              RAWPrerollRequestURL: {
                _text: prerollRequestUrl
              },
              PrerollRequestURL: {
                _text: requestURL
              },
              ValidationResult: {
                _text: JSON.stringify(validationResult)
              },
              WhatsOnStatus: {
                _text: JSON.stringify(whatsonResponse)
              }
            }
          };

          updatedResponse['vmap:VMAP'] = {
            ...updatedResponse['vmap:VMAP'],
            ...debugTag
          };

          logger('PREROLL_FINAL_RESPONSE', { debugTag }, LogLevel.dev);
        }

        return xmlParser.fromJSONtoXML(updatedResponse);
      }
    }
    return '';
  }
}
