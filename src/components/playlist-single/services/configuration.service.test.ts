import { Test, TestingModule } from '@nestjs/testing';
import { ConfigurationService, IConfigurationService } from './configuration.service';
import {
  BreaksConfigurationCacheService,
  ICacheProvider,
  WorkerConfigCacheService
} from '../../../libs';
import dayjs from 'dayjs';
import { Channel, IConfiguration } from 'adpod-tools';
import MockDate from 'mockdate';
import { CacheStub, TestStubCacheModule } from '../../../libs/testing';
import { PlaylistMode } from '../../../models/playlistMode.model';
import { AWSS3FileContentMock, TestAwsModule } from 'adpod-aws/dist/testing';
import { AWSS3FileContent } from 'adpod-aws';

describe('ConfigurationService', () => {
  let cacheProviderStub: CacheStub;
  let configurationService: IConfigurationService;
  let awsFileContentMock: AWSS3FileContentMock;

  const channel = Channel.tvn;
  const version = 'v1_0_0';
  let now: dayjs.Dayjs;

  beforeEach(async () => {
    jest.resetAllMocks();
    jest.clearAllMocks();

    MockDate.set('2025-05-14T12:00:00+02:00');
    now = dayjs('2025-05-14T12:00:00+02:00');

    const app: TestingModule = await Test.createTestingModule({
      imports: [TestStubCacheModule, TestAwsModule],
      providers: [
        BreaksConfigurationCacheService,
        WorkerConfigCacheService,
        {
          provide: IConfigurationService,
          useClass: ConfigurationService
        }
      ]
    }).compile();

    cacheProviderStub = app.get(ICacheProvider);
    awsFileContentMock = app.get(AWSS3FileContent);
    configurationService = app.get(IConfigurationService);

    await cacheProviderStub.set('workerConfig', {
      playlistSingleTimeThreshold: 30 * 60,
      scheduleConfigsAvailability: {
        last: 1,
        next: 2,
        keepOutOfRangeConfigs: true,
        outOfRangeConfigsTTL: 4
      }
    });
  });

  describe('by bid', () => {
    test('should return configuration from cache by bid', async () => {
      // arrange
      const configs = [
        {
          id: 'config-1',
          time: now.add(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: now.add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      await cacheProviderStub.set(createKey(now, channel, version), configs);

      // act
      const result = await configurationService.getConfiguration(
        channel,
        version,
        PlaylistMode.debug,
        undefined,
        'config-1'
      );

      // assert
      expect(result).not.toBeNull();
      expect(result).toEqual(configs[0]);
    });

    test('should return configuration from cache by bid found in past 7 days and set to cache', async () => {
      // arrange
      const configs = [
        {
          id: 'config-1',
          time: now.subtract(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: now.subtract(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      const bucketConfigs = [
        {
          id: 'config-4',
          time: now.subtract(4, 'days').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-5',
          time: now.subtract(5, 'days').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-6',
          time: now.subtract(5, 'days').add(30, 'minutes').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-7',
          time: now.subtract(6, 'days').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      await seedCache(configs);
      awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce(bucketConfigs);
      awsFileContentMock.getConfigFromBucket.mockResolvedValue([]);

      // act
      const result = await configurationService.getConfiguration(
        channel,
        version,
        PlaylistMode.debug,
        undefined,
        'config-5'
      );

      // assert
      expect(result).not.toBeNull();
      expect(result).toEqual(bucketConfigs[1]);
      expect(await getCacheItems([...configs, bucketConfigs[1]])).toHaveLength(4);
    });

    describe('with bid date', () => {
      test('should return configuration from cache by bid date', async () => {
        // arrange
        const configs = [
          {
            id: 'config-1',
            time: now.subtract(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
            channel,
            version
          },
          {
            id: 'config-2',
            time: now.subtract(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
            channel,
            version
          },
          {
            id: 'config-3',
            time: now.add(6, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
            channel,
            version
          }
        ] as IConfiguration[];

        await seedCache(configs);

        // act
        const result = await configurationService.getConfiguration(
          channel,
          version,
          PlaylistMode.debug,
          undefined,
          'config-3'
        );

        // assert
        expect(result).not.toBeNull();
        expect(result).toEqual(configs[2]);
      });

      test('should return configuration from bucket by bid date and set to cache', async () => {
        // arrange
        const configs = [
          {
            id: 'config-1',
            time: now.subtract(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
            channel,
            version
          },
          {
            id: 'config-2',
            time: now.subtract(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
            channel,
            version
          },
          {
            id: 'config-3',
            time: now.add(5, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
            channel,
            version
          }
        ] as IConfiguration[];

        const bucketConfigs = [
          {
            id: 'config-4',
            time: now.add(6, 'hour').add(30, 'minutes').format('YYYY-MM-DDTHH:mm:ssZ'),
            channel,
            version
          },
          {
            id: 'config-5',
            time: now.add(6, 'hour').add(45, 'minutes').format('YYYY-MM-DDTHH:mm:ssZ'),
            channel,
            version
          },
          {
            id: 'config-6',
            time: now.add(7, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
            channel,
            version
          }
        ] as IConfiguration[];

        await seedCache(configs);
        awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce(bucketConfigs);

        // act
        const result = await configurationService.getConfiguration(
          channel,
          version,
          PlaylistMode.debug,
          undefined,
          'config-5',
          now.add(6, 'hour').add(30, 'minutes').format('YYYY-MM-DDTHH:mm:ssZ')
        );

        // assert
        expect(result).not.toBeNull();
        expect(result).toEqual(bucketConfigs[1]);
        expect(await getCacheItems([...configs, ...bucketConfigs.slice(0, 1)])).toHaveLength(
          5
        );
      });
    });
  });

  describe('by time', () => {
    test('should return configuration from cache when time in range', async () => {
      // arrange
      const configs = [
        {
          id: 'config-1',
          time: now.add(45, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: now.subtract(20, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      await cacheProviderStub.set(createKey(now, channel, version), configs);

      // act
      const result = await configurationService.getConfiguration(
        channel,
        version,
        PlaylistMode.debug,
        'now'
      );

      // assert
      expect(result).not.toBeNull();
      expect(result).toEqual(configs[1]);
    });

    test('should return configuration from bucket when threshold is out of cache range and set to cache', async () => {
      // arrange
      const configs = [
        {
          id: 'config-1',
          time: now.add(3, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: now.add(4, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-3',
          time: now.add(4, 'hour').add(30, 'minutes').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-4',
          time: now.add(5, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce(configs);

      // act
      const result = await configurationService.getConfiguration(
        channel,
        version,
        PlaylistMode.debug,
        now.add(4, 'hour').format('YYYY-MM-DDTHH:mm:ssZ')
      );

      // assert
      expect(result).not.toBeNull();
      expect(result).toEqual(configs[1]);
      expect(await getCacheItems(configs.slice(1, 2))).toHaveLength(2);
    });

    test('should return configuration from cache when time is outside of range cache but time was requested previously', async () => {
      // arrange
      await cacheProviderStub.set('workerConfig', {
        playlistSingleTimeThreshold: 120,
        scheduleConfigsAvailability: {
          last: 1,
          next: 2,
          keepOutOfRangeConfigs: true,
          outOfRangeConfigsTTL: 4
        }
      });

      const configs = [
        {
          id: 'config-1',
          time: now.add(3, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: now.add(4, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce(configs);

      // act 1
      let result = await configurationService.getConfiguration(
        channel,
        version,
        PlaylistMode.debug,
        now.add(3, 'hour').format('YYYY-MM-DDTHH:mm:ssZ')
      );

      // assert 1
      expect(result).not.toBeNull();
      expect(result).toEqual(configs[0]);
      let cacheConfigs = await cacheProviderStub.get(
        createKey(dayjs(configs[0].time), channel, version)
      );
      expect(cacheConfigs).toHaveLength(1);
      expect(cacheConfigs).toMatchObject([configs[0]]);

      // act 2
      result = await configurationService.getConfiguration(
        channel,
        version,
        PlaylistMode.debug,
        now.add(3, 'hour').format('YYYY-MM-DDTHH:mm:ssZ')
      );

      // assert 2
      expect(result).not.toBeNull();
      expect(result).toEqual(configs[0]);
      cacheConfigs = await cacheProviderStub.get(
        createKey(dayjs(configs[0].time), channel, version)
      );
      expect(cacheConfigs).toHaveLength(1);
      expect(cacheConfigs).toMatchObject([configs[0]]);
    });
  });

  function createKey(date: dayjs.Dayjs, ch: Channel, v: string) {
    return `${date.set('minutes', 0).set('seconds', 0).format('YYYYMMDD_HH:mm:ss')}_${ch}_${v}_REQUEST_CACHE_BREAK_KEYS`;
  }

  async function seedCache(configs: IConfiguration[]) {
    const configsGroupedByChannelAndVersion = configs.reduce<Record<string, IConfiguration[]>>(
      (acc, config) => {
        const key = createKey(dayjs(config.time), config.channel, config.version);
        acc[key] = acc[key] ?? [];
        acc[key].push(config);

        return acc;
      },
      {}
    );

    await cacheProviderStub.setMany(
      Object.entries(configsGroupedByChannelAndVersion).map(([key, value]) => ({
        key,
        value
      }))
    );
  }

  async function getCacheItems(configs: IConfiguration[]) {
    const configsGroupedByChannelAndVersion = configs.reduce<Record<string, IConfiguration[]>>(
      (acc, config) => {
        const key = createKey(dayjs(config.time), config.channel, config.version);

        acc[key] = acc[key] ?? [];
        acc[key].push(config);

        return acc;
      },
      {}
    );

    return (
      await cacheProviderStub.getMany(
        Object.entries(configsGroupedByChannelAndVersion).map(([key]) => key)
      )
    ).flat();
  }
});
