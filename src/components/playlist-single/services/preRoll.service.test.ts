import { PrerollService } from './preRoll.service';
import { BreaksConfigurationCacheService } from '../../../libs/caching/services/breaksConfigurationCache.service';
import { CustomParamsGenerator } from '../../../scripts/services/customParamsGenerator.service';
import { Test, TestingModule } from '@nestjs/testing';
import { WhatsonEnum, WhatsonResponseType } from '../../whatson/whatson.service';
import { PlaylistMode } from '../../../models/playlistMode.model';
import { Channel } from 'adpod-tools';
import { Protocol } from '../../../models/protocol.model';
import * as requestModule from 'adpod-tools/dist/utils/fetch/request';
import { fullConfig } from '../../../assets/mocks/TEST/preroll/cacheResponse';
import { prerollResponseData } from '../../../assets/mocks/TEST/preroll/prerollResponseData';
import { RequestMacroParams } from '../../../scripts/configuration/injectReqParams/requestMacroParams';
import { CacheProviderMock, TestCacheModule } from '../../../libs/testing';
import { ICacheProvider } from '../../../libs/caching';

describe('PrerollService test suite', () => {
  const mockCustomParamsGenerate = jest.fn();
  let service: PrerollService;
  const requestMock = jest.spyOn(requestModule, 'request');
  let cacheServiceMock: CacheProviderMock;

  beforeEach(async () => {
    jest.resetAllMocks();
    jest.clearAllMocks();

    const app: TestingModule = await Test.createTestingModule({
      imports: [TestCacheModule],
      providers: [
        PrerollService,
        BreaksConfigurationCacheService,
        {
          provide: CustomParamsGenerator,
          useValue: {
            generate: mockCustomParamsGenerate
          }
        }
      ]
    }).compile();
    service = app.get(PrerollService);
    cacheServiceMock = app.get(ICacheProvider);
  });

  it('should return correct preroll Vmap', async () => {
    cacheServiceMock.getMany.mockResolvedValue(fullConfig);
    mockCustomParamsGenerate.mockResolvedValue('testCampaign=true&v=mock_v1_0_0_PREROLL');
    requestMock.mockResolvedValue(prerollResponseData);

    const channel = Channel.mtit;
    const version: string = 'mock_v1_0_0_PREROLL';
    const requestMacroParams = new RequestMacroParams(
      undefined,
      Protocol.http,
      '0',
      'testCampaign=true',
      Channel.mtit,
      '1',
      undefined,
      undefined
    );
    const headers: object = {
      'x-device-user-agent':
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
      'x-device-ip': '*************',
      'user-agent': 'PostmanRuntime/7.39.0',
      accept: '*/*',
      'cache-control': 'no-cache',
      'postman-token': '6874ae67-22a2-4ba6-8286-219e114b9b4c',
      host: 'localhost:3420',
      'accept-encoding': 'gzip, deflate, br',
      connection: 'keep-alive'
    };
    const mode = PlaylistMode.preroll_debug;
    const whatsonResponse: WhatsonResponseType = {
      channel: 'MTIT',
      currentTime: '2024-09-03T10:42:53+02:00',
      whatson: WhatsonEnum.content,
      changeInSeconds: 186,
      changeTime: '2024-09-03T10:45:59+02:00',
      content: {
        startTimeTimestamp: 1725352110000,
        startTimeFormatted: '2024-09-03T10:28:30+02:00',
        endTimeTimestamp: 1725353061000,
        endTimeFormatted: '2024-09-03T10:44:21+02:00',
        duration: '00:15:51:00',
        durationInSeconds: 951,
        EPGMetadata: {
          Language: { _text: 'IT' },
          Show: {
            ShowID: { _text: '19745' },
            EPGShowName: { _text: 'Ingegneria degli errori con Piergiorgio Odifreddi' }
          },
          Series: {
            SeriesID: { _text: '108675' },
            EPGSeriesName: { _text: 'Ingegneria degli Epic Fail' }
          },
          Prog: { TitleID: { _text: '3755652' }, EPGProgName: { _text: 'Episodio 7' } }
        }
      }
    };
    const spyApplyParams = jest.spyOn(requestMacroParams, 'applyParams');
    spyApplyParams.mockReturnValue(
      'http://5e529.v.fwmrm.net/ad/g/1?prof=386345%3ADNI_IT_HbbTV_SSAI_live&nw=386345&metr=1031&caid=ADDRESSABLE_TV_DUMMY_ASSET&asnw=386345&csid=ADDRESSABLE_TV_SWITCH_IN_SPOT_ITALY_MTIT&vprn=48920963&vip=*************&vdur=3600&resp=vmap1%2Bvast4&flag=+scpv+emcr+amcb+slcb+aeti+sltp;_fw_vcid2=&_fw_h_user_agent=Mozilla%2F5.0%20(Macintosh%3B%20Intel%20Mac%20OS%20X%2010_15_7)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Chrome%2F96.0.4664.110%20Safari%2F537.36;ptgt=a&_fw_gdpr=1&_fw_gdpr_consent=&mind=5&maxd=15&tpos=0&slau=Preroll%20Spot'
    );
    const result = await service.createPrerollResponse(
      channel,
      version,
      requestMacroParams,
      headers,
      mode,
      whatsonResponse
    );

    expect(result).toMatchSnapshot();
  });
});
