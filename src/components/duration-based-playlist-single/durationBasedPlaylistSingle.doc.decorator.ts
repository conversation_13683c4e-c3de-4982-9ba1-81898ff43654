import { applyDecorators } from '@nestjs/common';
import { ApiResponse, ApiHeader, ApiProduces } from '@nestjs/swagger';

export const ApiHeaders = () =>
  applyDecorators(
    ApiHeader({
      name: 'x-device-user-agent',
      required: false,
      description: '<PERSON><PERSON>’s device user agent.'
    }),
    ApiHeader({
      name: 'x-device-IP',
      required: false,
      description: 'Client’s device IP address.'
    }),
    ApiHeader({
      name: 'x-device-referrer',
      required: false,
      description: 'Client’s device referrer URL.'
    }),
    ApiHeader({
      name: 'x-tvn-links-response-proto',
      required: false,
      description:
        "AdServer and tracking URL's protocol. Request protocol is used if not provided."
    })
  );

export const ApiResponses = () =>
  applyDecorators(
    ApiProduces('application/xml'),
    ApiResponse({ status: 200, description: 'Valid response.' }),
    ApiResponse({ status: 304, description: 'Nothing was changed.' }),
    ApiResponse({ status: 400, description: 'Bad request.' }),
    ApiResponse({ status: 500, description: 'Server error.' })
  );
