import { Channel } from 'adpod-tools';
import { JoiSchema, JoiSchemaOptions } from 'nestjs-joi';
import Jo<PERSON> from 'joi';
import { isValidUriParams } from '../../../validators/isValidUriParams';
import { DurationBasedPlaylistMode } from '../../../models/playlistMode.model';
import { PlaylistOutputs } from '../../../models/playlistOutput.model';
import { ApiProperty } from '@nestjs/swagger';

@JoiSchemaOptions({
  allowUnknown: true,
  cache: true
})
export class GetDurationBasedPlaylistQueryDto {
  @JoiSchema(Joi.string().min(1).optional())
  @ApiProperty({
    name: 'uid',
    required: false,
    description: 'Unique user ID.',
    type: 'string'
  })
  uid?: string;

  @JoiSchema(
    Joi.string()
      .valid(...Object.values(Channel))
      .required()
  )
  @ApiProperty({
    name: 'ch',
    required: true,
    description: 'Broadcast channel id.',
    type: 'string'
  })
  ch: Channel;

  @JoiSchema(
    Joi.string()
      .valid(...Object.values(PlaylistOutputs))
      .optional()
  )
  @ApiProperty({
    name: 'output',
    required: false,
    description: 'Output format.',
    enum: PlaylistOutputs,
    default: PlaylistOutputs.default
  })
  output?: PlaylistOutputs;

  @JoiSchema(
    Joi.string().custom(isValidUriParams).empty('').optional().messages({
      'any.invalid': '"cust_params" must be valid string with uri params'
    })
  )
  @ApiProperty({
    name: 'cust_params',
    required: false,
    description:
      'Encoded key-values pairs separated by & (%26). If present the parameter will be used in adserver request.',
    type: 'string',
    example: 'paramOne%3D%27valueOne%27%26paramTwo%3DvalueTwo',
    default: ''
  })
  cust_params?: string;

  @JoiSchema(Joi.string().required())
  @ApiProperty({
    name: 'v',
    required: true,
    description: 'Config version',
    type: 'string'
  })
  v: string;

  @JoiSchema(Joi.number().min(5).required())
  @ApiProperty({
    name: 'duration',
    required: false,
    description:
      'Expected playlist duration in seconds. If playlist has different duration than the *duration* param value - empty vast will be returned.',
    type: 'number'
  })
  duration: number;

  @JoiSchema(
    Joi.valid(...Object.values(DurationBasedPlaylistMode))
      .empty('')
      .optional()
  )
  @ApiProperty({
    name: 'mode',
    required: false,
    description:
      'Playlist Mode. Possible values:\n\n* debug - AdServer ads + extra *&lt;Debug&gt;* tag.\n\n',
    enum: DurationBasedPlaylistMode
  })
  mode?: DurationBasedPlaylistMode;
}
