import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { DurationBasedPlaylistSingleController } from './durationBasedPlaylistSingle.controller';
import { BreaksConfigurationCacheService } from '../../libs/caching/services/breaksConfigurationCache.service';
import { FreewheelClient } from '../../clients/freewheel/freewheel.client';
import { IFreewheelClient } from '../../clients/freewheel/freewheelClient.interface';
import { IAdPromoServerClient } from '../../clients/adPromoServer/adPromoServerClient.interface';
import { AdPromoServerClient } from '../../clients/adPromoServer/adPromoServer.client';
import {
  DeapProfileService,
  IDeapProfilesService
} from '../../scripts/services/deapProfiles.service';
import {
  DurationBasedPlaylistSingleService,
  IDurationBasedPlaylistSingleService
} from './durationBasedPlaylistSingle.service';
import { requestContextMiddleware } from '../../libs/middlewares';

@Module({
  controllers: [DurationBasedPlaylistSingleController],
  providers: [
    BreaksConfigurationCacheService,
    {
      provide: IDurationBasedPlaylistSingleService,
      useClass: DurationBasedPlaylistSingleService
    },
    {
      provide: IDeapProfilesService,
      useClass: DeapProfileService
    },
    {
      provide: IFreewheelClient,
      useClass: FreewheelClient
    },
    {
      provide: IAdPromoServerClient,
      useClass: AdPromoServerClient
    },
    {
      provide: IDeapProfilesService,
      useClass: DeapProfileService
    }
  ]
})
export class DurationBasedPlaylistSingleModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(requestContextMiddleware).forRoutes('*');
  }
}
