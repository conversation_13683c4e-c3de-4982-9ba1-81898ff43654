import { Controller, Get, HttpException, HttpStatus, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { LogLevel } from 'adpod-tools';
import { GetWhatsonQuery } from './dtos/GetWhatsonQuery';
import { WhatsonResponseType, WhatsonService } from './whatson.service';
import { WhatsonDoc } from './whatson.doc.decorator';
import logger from '../../libs/logging/logger';

@Controller(['api/playlist', 'api'])
@ApiTags('whatson')
export class WhatsonController {
  constructor(private readonly whatsonService: WhatsonService) {}

  @Get('/whatson')
  @WhatsonDoc()
  async getWhatsonStatus(@Query() queryParams: GetWhatsonQuery): Promise<WhatsonResponseType> {
    try {
      return await this.whatsonService.checkBreaksSchedule(queryParams.ch);
    } catch (e: unknown) {
      logger('ERROR_UNKNOWN_SERVER_ERROR', { e }, LogLevel.error);
      if (e instanceof HttpException) throw e;
      if (e instanceof Error) {
        throw new HttpException(e.message, HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }

    return null;
  }
}
