import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import { Channel, LogLevel } from 'adpod-tools';
import { Injectable } from '@nestjs/common';
import logger from '../../libs/logging/logger';
import { BreaksConfigurationCacheService } from '../../libs/caching/services/breaksConfigurationCache.service';
import { WorkerConfigCacheService } from '../../libs';

dayjs.extend(isBetween);

export enum WhatsonEnum {
  adverts = 'ADVERTS',
  content = 'CONTENT'
}

export type WhatsonResponseType = {
  channel: string;
  currentTime: string;
  whatson: WhatsonEnum;
  changeInSeconds: number;
  changeTime: string;
  content: any;
} | null;

@Injectable()
export class WhatsonService {
  constructor(
    private readonly breaksConfigurationCacheService: BreaksConfigurationCacheService,
    private readonly workerConfigCacheService: WorkerConfigCacheService
  ) {}

  async getBreaksSchedule(
    channel: Channel
  ): Promise<{ breakStartTime: string; breakEndTime: string; content: any }[]> {
    const version = [Channel.ttv, Channel.tvn, Channel.tvn7].includes(channel)
      ? 'v1_0_0'
      : 'v1_0_0_preroll';

    const cacheConfigs = await this.breaksConfigurationCacheService.getConfigsFromCache(
      channel,
      version
    );

    logger('WHATSON_BREAK_SCHEDULE_CACHE_GET', { cacheConfigs }, LogLevel.whatson);

    return cacheConfigs.map((c) => ({
      breakStartTime: c.time,
      breakEndTime: dayjs(c.time).add(c.duration, 'seconds').format('YYYY-MM-DDTHH:mm:ssZ'),
      content: c.metadata?.contentSegments
    }));
  }

  async checkBreaksSchedule(channel: Channel): Promise<WhatsonResponseType> {
    const dayJsObj = dayjs();
    const currentTime = dayJsObj.format('YYYY-MM-DDTHH:mm:ssZ');

    logger('WHATSON_REQUEST', { currentTime }, LogLevel.whatson);

    const breaksDateTimeRanges = await this.getBreaksSchedule(channel);

    logger('WHATSON_BREAK_DATE_RANGES', { breaksDateTimeRanges }, LogLevel.whatson);

    if (breaksDateTimeRanges.length) {
      const currentBreak = breaksDateTimeRanges.find((b) =>
        dayjs(currentTime).isBetween(b.breakStartTime, b.breakEndTime)
      );

      const isWithinBreakRange = !!currentBreak;

      logger('WHATSON_CURRENT_BREAK', { currentBreak, isWithinBreakRange }, LogLevel.whatson);

      const nextBreak = breaksDateTimeRanges.find((b) =>
        dayjs(b.breakStartTime).isAfter(currentTime)
      );

      logger('WHATSON_NEXT_BREAK', { nextBreak }, LogLevel.whatson);

      const nextBreakStartTime = nextBreak?.breakStartTime;

      const currentBreakEndTime = currentBreak?.breakEndTime;

      const timeToNextBreak = dayjs(nextBreakStartTime).unix() - dayjs(currentTime).unix();

      const timeToNextContent = dayjs(currentBreakEndTime).unix() - dayjs(currentTime).unix();

      logger(
        'WHATSON_TIMES',
        { nextBreakStartTime, currentBreakEndTime, timeToNextBreak, timeToNextContent },
        LogLevel.whatson
      );

      const content = !isWithinBreakRange
        ? nextBreak?.content?.find(
            (c) =>
              dayjs(currentTime).isBetween(c.startTimeFormatted, c.endTimeFormatted) &&
              c.EPGMetadata !== undefined
          )
        : null;

      const whatsonStatus = isWithinBreakRange
        ? WhatsonEnum.adverts
        : content
          ? WhatsonEnum.content
          : WhatsonEnum.adverts;

      logger('WHATSON_STATUS', { content, whatsonStatus }, LogLevel.whatson);

      const whatsonResponse = {
        channel,
        currentTime,
        whatson: whatsonStatus,
        changeInSeconds: isWithinBreakRange ? timeToNextContent : timeToNextBreak,
        changeTime: `${isWithinBreakRange ? currentBreakEndTime : nextBreakStartTime}`,
        content
      };

      logger('WHATSON_RESPONSE', whatsonResponse, LogLevel.whatson);

      return whatsonResponse;
    }

    logger('WHATSON_WITHOUT_BREAK_DATE_RANGES', { breaksDateTimeRanges }, LogLevel.whatson);

    return null;
  }
}
