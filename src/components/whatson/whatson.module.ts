import { Modu<PERSON> } from '@nestjs/common';
import { WhatsonController } from './whatson.controller';
import { WhatsonService } from './whatson.service';
import { BreaksConfigurationCacheService } from '../../libs/caching/services/breaksConfigurationCache.service';

@Module({
  controllers: [WhatsonController],
  providers: [WhatsonService, BreaksConfigurationCacheService]
})
export class WhatsonModule {}
