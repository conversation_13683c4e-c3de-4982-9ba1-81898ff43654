#!/bin/bash

# Loading default configuration
set -a
if [[ -e docker/project.config ]]; then
  . docker/project.config;
fi

# Loading environment configuration using secret file (if exists)
if [[ -e /run/secrets/env_secrets ]]; then
  . /run/secrets/env_secrets;
fi
set +a

# Recreating app configuration from jinja2 templates (.j2)
# Variable used in templates should be at least in docker/project.config file
#for template_file in $(find . -type f -not \( -path '*node_modules*' \) -name '*.j2' -prune );
#do
#  target_file=$(sed 's|\.j2$||g' <<< ${template_file});
#  echo ">recreating file from template: $target_file";
#  python3 -c "import os,sys,jinja2; sys.stdout.write(jinja2.Template(sys.stdin.read()).render(env=os.environ))" < ${template_file} > ${target_file};
#done;

# For AWS.
mkdir -p /home/<USER>/.aws/
cat << EOF > /home/<USER>/.aws/config
[profile ${AWS_PROFILE}]
region = eu-central-1
output = json
EOF
cat << EOF > /home/<USER>/.aws/credentials
[${AWS_PROFILE}]
aws_access_key_id = ${AWS_ACCESS_KEY_ID}
aws_secret_access_key = ${AWS_SECRET_ACCESS_KEY}
EOF


# old script, but new should be compatible
cd /home/<USER>
mkdir -p /home/<USER>/src/assets/logs # <- Moved to new Dockerfiles
