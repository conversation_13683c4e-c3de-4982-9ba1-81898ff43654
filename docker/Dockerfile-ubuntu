# ====== Ubuntu build ========

FROM ubuntu:focal as builder

ARG DEBIAN_FRONTEND=noninteractive

ENV TZ=Europe/Warsaw

WORKDIR /home/<USER>/

RUN apt-get update && DEBIAN_FRONTEND=noninteractive && apt-get install -y \
  tzdata \
  curl \
  git \
  iputils-ping \
  zsh \
  build-essential \
  python3-jinja2 \
  && ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone \
  && dpkg-reconfigure --frontend noninteractive tzdata \
  && curl -sL https://deb.nodesource.com/setup_22.x | bash - \
  && apt-get install -y nodejs \
  && apt-get clean \
  && mkdir -p /home/<USER>/ \
  && useradd -d /home/<USER>/ ad \
  && chown ad:ad /home/<USER>/

COPY --chown=ad:ad . /home/<USER>/

RUN cd /home/<USER>
  && su - ad -c 'npm config set registry https://artifacts.nvtvt.com/artifactory/api/npm/npm-adtech-adpod-virtual/' \
  && su - ad -c 'npm install' \
  && su - ad -c 'mkdir -p /home/<USER>/src/assets/logs'

USER ad


# ====== Ubuntu app ========
FROM ubuntu:focal as app
ARG DEBIAN_FRONTEND=noninteractive

ENV TZ=Europe/Warsaw

WORKDIR /home/<USER>/

RUN apt-get update && DEBIAN_FRONTEND=noninteractive && apt-get install -y \
  tzdata \
  curl \
  zip \
  && ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone \
  && dpkg-reconfigure --frontend noninteractive tzdata \
  && curl -sL https://deb.nodesource.com/setup_22.x | bash - \
  && apt-get install -y nodejs \
  && apt-get clean \
  && mkdir -p /home/<USER>/ \
  && useradd -d /home/<USER>/ ad \
  && chown ad:ad /home/<USER>/ \
  && curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "/root/awscliv2.zip" -s \
  && unzip -uoq /root/awscliv2.zip \
  && ./aws/install -i /usr/local/aws-cli -b /usr/local/bin \
  && aws --version \
  && rm -f /root/awscliv2.zip

COPY --from=builder --chown=ad:ad /home/<USER>/ /home/<USER>/

EXPOSE 3420
EXPOSE 10060
USER ad

ADD docker/config/entrypoint_ci.sh /usr/local/bin/entrypoint.sh
ENTRYPOINT ["entrypoint.sh"]

HEALTHCHECK --interval=60s --timeout=5s --start-period=20s \
  CMD curl -s -f http://localhost:10060/info/check -o /dev/null --write-out '%{http_code}' | grep -P '200' || exit 1


