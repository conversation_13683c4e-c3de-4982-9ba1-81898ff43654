# ====== Alpine build ========

FROM node:22.14.0-alpine AS alpine

RUN apk --no-cache add curl tzdata zsh bash build-base python3 py3-jinja2

ENV TZ=Europe/Warsaw
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone \
  && cp /usr/share/zoneinfo/${TZ} /etc/localtime && echo "${TZ}" >  /etc/timezone \
  && apk del tzdata \
  && rm -rf /var/cache/apk/* \
  && mkdir -p /home/<USER>/ \
  && adduser --disabled-password --home "/home/<USER>/" --no-create-home ad \
  && chown ad:ad /home/<USER>/

WORKDIR /home/<USER>/

COPY --chown=ad:ad . /home/<USER>/

RUN cd /home/<USER>
  && su - ad -c 'npm install' \
  && su - ad -c 'mkdir -p /home/<USER>/src/assets/logs'

EXPOSE 3420
USER ad
VOLUME [/home/<USER>/src/assets/logs]

ADD docker/config/entrypoint_ci.sh /usr/local/bin/docker/config/entrypoint.sh
ENTRYPOINT ["entrypoint.sh"]

HEALTHCHECK --interval=20s --timeout=5s --start-period=20s \
  CMD curl -f http://localhost:3420/info/check 2>&1 | grep -P '^OK' || exit 1
