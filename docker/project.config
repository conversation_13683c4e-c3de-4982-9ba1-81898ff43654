#!/bin/bash

# zakomentowałem - nie wiem co to, a wali błędami bo nie ma takich plików w repo 
# cp $DIR1/extra_hosts $DIR2/config/extra_hosts

export DOCKER_IMAGE="ad-decisioning-image"
export DOCKER_CONTAINER="ad-decisioning"
export DOCKER_PORTS="-p 80:3420"

# APP DEFAULTS ( will be overwritten by secret file)
export NEW_RELIC_AGENT_ENABLED="false"
export NEW_RELIC_AGENT_LICENSE_KEY=""
export ENVIRONMENT_NAME="UNKNOWN"
