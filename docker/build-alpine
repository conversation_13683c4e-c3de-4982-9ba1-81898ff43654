#!/bin/bash

DIR1="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null && pwd )"

cd $DIR1/../

DIR2="$(pwd)"

#echo "DIR1:$DIR1"
#echo "DIR2:$DIR2"


source $DIR2/docker/project.config

docker build  -t $DOCKER_IMAGE -f docker/Dockerfile.alpine .
#docker image tag $DOCKER_IMAGE:latest $DOCKER_IMAGE:$(python3 -c 'import json, sys; sys.stdout.write(json.load(open("package.json"))["version"])')

docker container rm -f $DOCKER_CONTAINER

docker run \
    --name $DOCKER_CONTAINER \
    $DOCKER_PORTS \
    -d $DOCKER_IMAGE
