FROM ubuntu:focal

ENV DEBIAN_FRONTEND noninteractive

RUN apt-get update && DEBIAN_FRONTEND=noninteractive && apt-get install -y \
  tzdata \
  curl \
  git \
  iputils-ping \
  zsh 

ENV TZ=Europe/Warsaw
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

RUN dpkg-reconfigure --frontend noninteractive tzdata
 
RUN apt-get clean 

RUN curl -sL https://deb.nodesource.com/setup_22.x | bash -
RUN apt-get install -y nodejs

RUN mkdir /root/.ssh/
COPY config/ssh/* /root/.ssh/
RUN chmod 400 /root/.ssh/id_rsa

RUN echo 8
RUN git clone ssh://********************:7999/adt/ad-decisioning.git /home/<USER>

RUN cd /home/<USER>

ADD config/entrypoint.sh /usr/local/bin
ENTRYPOINT ["entrypoint.sh"]