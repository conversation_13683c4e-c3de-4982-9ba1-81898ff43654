{"env": {"node": true, "es2022": true, "jest": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "airbnb-base", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2022, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["prettier", "@typescript-eslint/eslint-plugin"], "rules": {"camelcase": "off", "no-console": "off", "no-plusplus": "off", "no-unused-vars": "off", "operator-linebreak": "off", "function-paren-newline": "off", "implicit-arrow-linebreak": "off", "no-param-reassign": "off", "guard-for-in": "off", "no-shadow": "off", "no-nested-ternary": "off", "radix": "off", "default-case": "off", "no-continue": "off", "no-case-declarations": "off", "no-await-in-loop": "off", "no-return-assign": "off", "class-methods-use-this": "off", "linebreak-style": "off", "comma-dangle": "off", "arrow-body-style": "off", "no-extra-boolean-cast": "off", "eol-last": "warn", "max-len": "off", "eqeqeq": "warn", "no-useless-escape": "warn", "no-unsafe-optional-chaining": "warn", "no-use-before-define": "off", "no-useless-constructor": "off", "no-return-await": "off", "default-param-last": "off", "no-empty-function": "off", "no-restricted-syntax": "off", "no-underscore-dangle": "off", "import/prefer-default-export": "off", "import/extensions": "off", "import/no-unresolved": "off", "import/no-mutable-exports": "off", "import/no-extraneous-dependencies": "off", "import/newline-after-import": "off", "import/no-duplicates": "off", "import/order": "off", "import/first": "off", "prefer-promise-reject-errors": "warn", "no-duplicate-imports": "warn", "prefer-const": "warn", "max-classes-per-file": "off", "object-shorthand": "warn", "@typescript-eslint/indent": "off", "@typescript-eslint/interface-name-prefix": "off", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-non-null-asserted-optional-chain": "off", "@typescript-eslint/no-inferrable-types": "off", "@typescript-eslint/no-var-requires": "warn", "@typescript-eslint/no-empty-function": "warn", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-shadow": "warn", "@typescript-eslint/no-require-imports": "off", "@typescript-eslint/no-empty-object-type": "off", "@typescript-eslint/no-useless-constructor": "warn", "@typescript-eslint/default-param-last": "warn", "@typescript-eslint/no-floating-promises": "off", "@typescript-eslint/no-explicit-any": "off", "no-void": "off", "prettier/prettier": ["off", {"endOfLine": "auto"}], "@typescript-eslint/naming-convention": ["off", {"selector": "interface", "format": ["PascalCase"], "prefix": ["I"]}, {"selector": "variable", "format": ["camelCase", "UPPER_CASE"]}, {"selector": "typeLike", "format": ["PascalCase"]}, {"selector": "parameter", "format": ["camelCase", "PascalCase"]}]}}